<?php

namespace App\Http\Controllers\Channel;

use Apimio\MappingConnectorPackage\Http\Controllers\MappingFieldController;
use Apimio\MappingConnectorPackage\models\Template;
use App\Classes\Mapping\Conversion;
use App\Classes\Shopify\ArrayCreate;
use App\Classes\Shopify\ConvertToShopifyArray;
use App\Http\Controllers\Controller;
use App\Jobs\SyncShopifyProductJob;
use App\Models\Channel\ChannelProduct;
use App\Models\Channel\ChannelVersion;
use App\Models\Product\Family;
use App\Models\Product\Product;
use App\Traits\AWSSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class TemplateController extends Controller
{
    use AWSSetting;
    public function create($id){

        $mapping = new MappingFieldController();

        // this method is for apimio products data
        $data['families'] = Family::with('attributes')->get();
        $mapping_array['request_data'] = [
            'template_method_type' => "shopify"
        ];

        $data = array_merge($data, $mapping->create_apimio_left_array($mapping_array));
        $data = array_merge($data, $mapping_array);

        // only send families for fetching heading of attributes
//        $data['left_array'] = $mapping->fetch_apimio_products(null, true,$data);
        $data['left_array'] = Template::apimio_mapping_array([], false);
        $data['right_array'] = (new ArrayCreate($id,auth()->user()->organization_id))->getDefinitions()->mainArray();

        // fetch first version id base on channel id for default template mapping
        $channel_version = ChannelVersion::where('channel_id' ,$id)->first();


        $data_required = [
            'organization_id' => auth()->user()->organization_id,
            'output_type' => 'shopify',
            'template_method_type' => 'shopify', //its only contain ('import','export','shopify','clone','other')
            'sync' => false,
            'apimio_plans_access' => [
                'prime_user' => true, // Gate::allows('primeOrPaidUser')
                'template_save' => true, //Gate::allows('SubscriptionAccess','import-template-save')
            ],
            'redirect_url_route' => 'shopify.template.redirect', //it should be post method
            'versions' => Conversion::fetch_all_version_in_array(),
            'catalogs' => Conversion::fetch_all_catalog_in_array(),
            'locations' => Conversion::fetch_all_location_in_array(),
            'default_template_mapping' => (new ArrayCreate($id,auth()->user()->organization_id, $channel_version->id ?? null))->default_shopify_template_mapping()
        ];

        return $mapping->mapping_view($data['left_array'],$data['right_array'],$data_required);
    }


    public function redirection(Request $request){

        $validator = Validator::make($request->all(), [
            'file_path' => 'required',
            'nodes' => 'required',
            'organization_id' => 'required',
        ]);
        if (!file_exists(Storage::disk('public')->path($request->file_path))){
            $validator->errors()->add('file_path', 'Your product data json file is not exist.');
            return $validator->errors();
        }
        (new MappingFieldController())->mapping_convert($request);


        if (isset($request->save_template)){
            $msg = 'Template saved successfully.';
            return redirect()->route('channel.index')->with(['success'=>'Template update successfully.']);
        }

        return redirect()->route('channel.index')->withErrors(['main'=>'Something went wrong.']);
    }


    public function bulkSync(Request $request)
    {
        //get the version of the channel
        $version_id = ChannelVersion::query()
                ->where('channel_id',$request->channel_id)
                ->value('version_id')??null;

        //get the template of the channel
        $template_ids = Template::query()
            ->where('channel_id', json_encode((string) $request->channel_id))
            ->where('version_id', json_encode((string) $version_id))
            ->where('type', 'shopify')
            ->get()
            ->pluck('id')
            ->toArray();


        $product_ids = explode(',',$request->product_id);

        if(env('CUSTOM_QUEUE_WORKER') == 'local'){

        }else{
            $queueUrl = $this->createFifoSQS(auth()->user()->organization_id.'-'.$request->channel_id);
        }

        //change the status of product to syncing start
        (new ChannelProduct())->changeProductSyncStatus(['channel_id'=>$request->channel_id,'product_ids'=>$product_ids,'status'=> -1 ]);

        // status queue for syncing products
        $data['product_id'] =$product_ids;
        $data['channel_id'] = $request->channel_id;
        $data['organization_id'] = auth()->user()->organization_id;
        $data['user'] = auth()->user();
        $data['template_ids'] = (count($template_ids) >0) ?$template_ids:null ;


        // if(env('CUSTOM_QUEUE_WORKER') == 'local'){
        //     dispatch(new SyncShopifyProductJob($data));
        // }else{
        //     $data['queueUrl'] = $queueUrl;
        //     $jobs = [
        //         new SyncShopifyProductJob($data)
        //     ];
        //     $this->dispatchJobsToFifoQueue($jobs, $request->channel_id, null, $queueUrl);
        // }

        //TODO:THIS BELOW CODE AFTER TESTING THE SYNCING PROCESS

    //    return $this->testingSyncProduct($product_ids,$request);

        return redirect()->back()->with(['success'=>"We're starting the process to sync the products. You'll get a notification when it's done."]);

    }

    public function testingSyncProduct($product_ids,$request ){

        /*TODO: NEW CODE FOR IMAGES*/
        $product_id = $product_ids[0] ;
        //  $channel_id = Channel::where('organization_id' , auth()->user()->organization_id)->get()->first()->value('id');
        $channel_id = $request->channel_id;

        // fetch first version id base on channel id for default template mapping
        $channel_version = ChannelVersion::where('channel_id' ,$channel_id)->first();

        $products_obj = new Product();

        $products = $products_obj->with(
            [
                "brands",
                "channel.shopify_status",
                "brands",
                "categories.channel"=>function($q) use ($channel_id){
                    $q->where('channel_id',$channel_id)
                        ->where('store_connect_type','shopify');
                },
                "vendors",
                "channels"=>function($q) use ($channel_id){
                    $q->where('channel_id',$channel_id);
                },
                "variants" => function($q) use ($channel_version){
                    $q->with('settings','channelVariant','file')->where('version_id',$channel_version->version_id);
                },
                "ChannelVariants"=> function($query) use($channel_id){
                    $query->with('variant.file')->where(["channel_id" => $channel_id , "store_connect_type" => "shopify"]);
                },
                'versions'=>function($q) use ($channel_version){
                    $q->where('versions.id',$channel_version->version_id);
                },
                "files.channelFile" => function($query) use($channel_id){
                    $query->where(["channel_id" => $channel_id , "store_connect_type" => "shopify"]);
                }

            ])
            ->where('id',$product_id)
            ->get();




        if (is_iterable($products)) {
            $i = 0;
            foreach ($products as $product) {
                $products[$i] = $products_obj->product_fetch($product);
                $i++;
            }
        }

        $shopify_id =  $products[0]->channel?$products[0]->channel->shopify_status?$products[0]->channel->shopify_status->response:null:null;

        //get the version of the channel
        $version_id = ChannelVersion::query()
                ->where('channel_id',$request->channel_id)
                ->value('version_id')??null;


        //get the template of the channel
        $template_ids = Template::query()
            ->where('channel_id', json_encode((string) $channel_id))
            ->where('version_id', json_encode((string) $version_id))
            ->where('type', 'shopify')
            ->get()
            ->pluck('id')
            ->toArray();


//          dd($products);
        $mapping = new MappingFieldController();
        // this method is for apimio products data
        $data_required = [
            'organization_id' => auth()->user()->organization_id,
            'output_type' => 'shopify',
            'template_method_type' => 'shopify', //its only contain ('import','export','shopify','clone','other')
            'sync' => true,  //true or false
            'apimio_plans_access' => [
                'prime_user' => true, // Gate::allows('primeOrPaidUser')
                'template_save' => true, //Gate::allows('SubscriptionAccess','import-template-save')
            ],
            'template_id' => empty($template_ids) ? null: $template_ids[0], // template array
            'redirect_url_route' => 'shopify.redirection', //it should be post method
            'versions' => Conversion::fetch_all_version_in_array(),
            'catalogs' => Conversion::fetch_all_catalog_in_array(),
            'locations' => Conversion::fetch_all_location_in_array(),
            'default_template_mapping' => (new ArrayCreate($channel_id,auth()->user()->organization_id, $channel_version->id ?? null))->default_shopify_template_mapping(),
            'products' => $products,
        ];

        // left array for apimio
        $data['left_array'] = Template::apimio_mapping_array([], false);

        // right array for shopify
//        $data['right_array'] = $mapping->fetch_right_array('shopify');
        $data['right_array'] = (new ArrayCreate($channel_id,auth()->user()->organization_id))->getDefinitions()->mainArray();


        $shopify_array =  $mapping->mapping_view($data['left_array'],$data['right_array'],$data_required);
// dd($shopify_array);
        if($shopify_array){

            $data =  (( new ConvertToShopifyArray([
                'product'=>$products,
                'channel_id'=>$request->channel_id,
                'organization_id'=>auth()->user()->organization_id,
                'shopify_array'=>$shopify_array['nodes']
            ])
            )->convert()->create(function($error){
                return back()->withErrors($error);
            },function(){
                return redirect()->back()->with(['success'=>'Product sync successfully.']);
            } ));
            return $data;
        }else{
            return back()->withErrors(['main'=>'Something went wrong.']);
        }

        /*
        $batch = Bus::batch([])->then(function(Batch $batch){

            $details = [
                'subject' => 'Syncing products to Shopify Successful.',
                'greeting' => '',
                'body' =>  "All products sync successfully to your shopify store.",
                'thanks' => '',
                'actionText' => 'view',
                'actionURL' => '' ,
                'user_id' =>$this->user->id,
                'organization_id' =>$this->organization_id,
            ];

            $notifier = new ApimioNotification($details);

            //un-comment if you want to stop notification in email
            $notifier->only_db_notify(true);
            $this->user->notify($notifier);

        })->dispatch();
        foreach ($product_ids as $product_id){
            $data['product_id'] = $product_id;
            $batch->add(new \App\Jobs\Shopify\SyncSingleProduct($data));
        }*/

    }



}
