{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "ff6e7543074859e4e9ad47e53e3f248f", "packages": [{"name": "apimio/completeness", "version": "v4.0.18", "source": {"type": "git", "url": "https://<EMAIL>/apimio/completeness.git", "reference": "24e7c9653208a7f60aa16da77294b7c584be9f62"}, "type": "library", "extra": {"laravel": {"providers": ["Apimio\\Completeness\\Providers\\CompletenessProvider"]}}, "autoload": {"psr-4": {"Apimio\\Completeness\\": "src"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Completeness and validation stats of your production information.", "time": "2025-06-20T08:11:22+00:00"}, {"name": "apimio/gallery", "version": "v5.0.24", "source": {"type": "git", "url": "https://<EMAIL>/apimio/gallery.git", "reference": "e15e234fa2385f9dc537cfbf6f4ba7f4408bfeb9"}, "require-dev": {"mockery/mockery": "^1.5", "orchestra/testbench": "7.x-dev", "phpunit/phpunit": "9.6.x-dev"}, "type": "library", "extra": {"laravel": {"providers": ["Apimio\\Gallery\\Providers\\GalleryProvider"]}}, "autoload": {"psr-4": {"Apimio\\Gallery\\": "src/"}}, "autoload-dev": {"psr-4": {"Apimio\\Gallery\\Tests\\": "tests/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Awesome gallery to manage your images in folders, analyze image quality, and re-use images in your product data.", "time": "2025-04-10T12:01:04+00:00"}, {"name": "apimio/mapping-connector-package", "version": "v8.9", "source": {"type": "git", "url": "https://<EMAIL>/apimio/mapping-fields-package.git", "reference": "b141e1b4fd408616d3d1b65de9b69b668a192778"}, "type": "library", "extra": {"laravel": {"providers": ["Apimio\\MappingConnectorPackage\\MappingFieldsServiceProvider"]}}, "autoload": {"psr-4": {"Apimio\\MappingConnectorPackage\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This package is use to map product attributes according to provided formulas.", "time": "2025-06-24T11:32:08+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/d71d9906c7bb63a28295447ba12e74723bd3730e", "reference": "d71d9906c7bb63a28295447ba12e74723bd3730e", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.7"}, "time": "2024-10-18T22:15:13+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.337.3", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "06dfc8f76423b49aaa181debd25bbdc724c346d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/06dfc8f76423b49aaa181debd25bbdc724c346d6", "reference": "06dfc8f76423b49aaa181debd25bbdc724c346d6", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}, "exclude-from-classmap": ["src/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.337.3"}, "time": "2025-01-21T19:10:05+00:00"}, {"name": "barryv<PERSON>/laravel-translation-manager", "version": "v0.6.8", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-translation-manager.git", "reference": "fe182d8b6e994ef92ff816c926d2c8c875bcb12d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-translation-manager/zipball/fe182d8b6e994ef92ff816c926d2c8c875bcb12d", "reference": "fe182d8b6e994ef92ff816c926d2c8c875bcb12d", "shasum": ""}, "require": {"illuminate/support": "^9|^10|^11|^12", "illuminate/translation": "^9|^10|^11|^12", "php": "^8.1", "symfony/finder": "^6|^7"}, "require-dev": {"orchestra/testbench": "^7|^8|^9|^10"}, "suggest": {"tanmuhittin/laravel-google-translate": "If you want to translate using Google API"}, "type": "library", "extra": {"laravel": {"providers": ["Barryvdh\\TranslationManager\\ManagerServiceProvider"]}, "branch-alias": {"dev-master": "0.6-dev"}}, "autoload": {"psr-4": {"Barryvdh\\TranslationManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "<PERSON><PERSON> Laravel Translations", "keywords": ["laravel", "translations", "translator"], "support": {"issues": "https://github.com/barryvdh/laravel-translation-manager/issues", "source": "https://github.com/barryvdh/laravel-translation-manager/tree/v0.6.8"}, "time": "2025-06-07T14:31:37+00:00"}, {"name": "bref/bref", "version": "2.4.7", "source": {"type": "git", "url": "https://github.com/brefphp/bref.git", "reference": "1c373f2bbdc0ca1e5657f80d552e63a0cd0cef65"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brefphp/bref/zipball/1c373f2bbdc0ca1e5657f80d552e63a0cd0cef65", "reference": "1c373f2bbdc0ca1e5657f80d552e63a0cd0cef65", "shasum": ""}, "require": {"crwlr/query-string": "^1.0.3", "ext-curl": "*", "ext-json": "*", "hollodotme/fast-cgi-client": "^3.0.1", "nyholm/psr7": "^1.4.1", "php": ">=8.0", "psr/container": "^1.0|^2.0", "psr/http-message": "^1.0|^2.0", "psr/http-server-handler": "^1.0", "riverline/multipart-parser": "^2.1.2", "symfony/process": "^4.4|^5.0|^6.0|^7.0"}, "require-dev": {"async-aws/core": "^1.0", "async-aws/lambda": "^1.0", "aws/aws-sdk-php": "^3.172", "bref/secrets-loader": "^1.0", "dms/phpunit-arraysubset-asserts": "^0.4", "doctrine/coding-standard": "^8.0", "guzzlehttp/guzzle": "^7.5", "phpstan/phpstan": "^1.10.26", "phpunit/phpunit": "^9.6.10", "symfony/console": "^4.4|^5.0|^6.0|^7.0", "symfony/yaml": "^4.4|^5.0|^6.0|^7.0"}, "bin": ["bref", "src/bref-local"], "type": "project", "autoload": {"psr-4": {"Bref\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Bref is a framework to write and deploy serverless PHP applications on AWS Lambda.", "homepage": "https://bref.sh", "keywords": ["aws", "bref", "faas", "lambda", "serverless"], "support": {"issues": "https://github.com/brefphp/bref/issues", "source": "https://github.com/brefphp/bref/tree/2.4.7"}, "funding": [{"url": "https://bref.sh/#enterprise", "type": "custom"}, {"url": "https://github.com/mnapoli", "type": "github"}], "time": "2025-06-25T11:57:14+00:00"}, {"name": "bref/laravel-bridge", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/brefphp/laravel-bridge.git", "reference": "a748a4bf1fbcfc89d3f6bb872192682297b32aa9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brefphp/laravel-bridge/zipball/a748a4bf1fbcfc89d3f6bb872192682297b32aa9", "reference": "a748a4bf1fbcfc89d3f6bb872192682297b32aa9", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.222", "bref/bref": "^2.1.8", "bref/laravel-health-check": "^1", "bref/monolog-bridge": "^1.0", "illuminate/container": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/contracts": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/http": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/queue": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/support": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "laravel/octane": "^1.2 || ^2.0", "php": "^8.0", "riverline/multipart-parser": "^2.0"}, "require-dev": {"larastan/larastan": "^2.11 || ^3", "orchestra/testbench": "^7.13 || ^9.0 || ^10.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpunit/phpunit": "^9.5 || ^10 || ^11", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "extra": {"laravel": {"providers": ["Bref\\LaravelBridge\\BrefServiceProvider"]}}, "autoload": {"files": ["src/bref-init.php"], "psr-4": {"Bref\\LaravelBridge\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "An advanced Laravel integration for Bref, including Octane support.", "homepage": "https://bref.sh/docs/frameworks/laravel.html", "keywords": ["aws", "bref", "faas", "lambda", "serverless"], "support": {"issues": "https://github.com/brefphp/laravel-bridge/issues", "source": "https://github.com/brefphp/laravel-bridge/tree/2.6.0"}, "time": "2025-06-14T15:19:29+00:00"}, {"name": "bref/laravel-health-check", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/brefphp/laravel-health-check.git", "reference": "fb2f8adbccfb381483035a4437ce45d6ad268955"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brefphp/laravel-health-check/zipball/fb2f8adbccfb381483035a4437ce45d6ad268955", "reference": "fb2f8adbccfb381483035a4437ce45d6ad268955", "shasum": ""}, "require": {"illuminate/console": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/contracts": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/database": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/http": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "illuminate/support": "^8.0 || ^9.0 || ^10.0 || ^11.0 || ^12.0", "php": ">=8.0"}, "require-dev": {"phpstan/phpstan": "^1", "phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Bref\\LaravelHealthCheck\\BrefHealthCheckServiceProvider"]}}, "autoload": {"psr-4": {"Bref\\LaravelHealthCheck\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Bref health checks for Laravel applications", "support": {"issues": "https://github.com/brefphp/laravel-health-check/issues", "source": "https://github.com/brefphp/laravel-health-check/tree/1.0.1"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}], "time": "2025-02-25T10:34:39+00:00"}, {"name": "bref/monolog-bridge", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/brefphp/monolog-bridge.git", "reference": "0dd46623b4a298838ca103f93d8cb29db029cc30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brefphp/monolog-bridge/zipball/0dd46623b4a298838ca103f93d8cb29db029cc30", "reference": "0dd46623b4a298838ca103f93d8cb29db029cc30", "shasum": ""}, "require": {"monolog/monolog": "^2.4|^3", "php": ">=8.0"}, "require-dev": {"mnapoli/hard-mode": "^0.3", "phpstan/phpstan": "^1", "phpunit/phpunit": "^9.6|^10.5|^11|^12"}, "type": "library", "autoload": {"psr-4": {"Bref\\Monolog\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Monolog formatter optimized for AWS Lambda and CloudWatch", "keywords": ["aws", "bref", "cloudwatch", "lambda", "monolog"], "support": {"issues": "https://github.com/brefphp/monolog-bridge/issues", "source": "https://github.com/brefphp/monolog-bridge/tree/1.0.1"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}], "time": "2025-06-14T15:06:36+00:00"}, {"name": "brick/math", "version": "0.12.3", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/866551da34e9a618e64a819ee1e01c20d8a588ba", "reference": "866551da34e9a618e64a819ee1e01c20d8a588ba", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.12.3"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-02-28T13:11:00+00:00"}, {"name": "carbonphp/carbon-doctrine-types", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon-doctrine-types.git", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon-doctrine-types/zipball/99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "reference": "99f76ffa36cce3b70a4a6abce41dba15ca2e84cb", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"doctrine/dbal": "<3.7.0 || >=4.0.0"}, "require-dev": {"doctrine/dbal": "^3.7.0", "nesbot/carbon": "^2.71.0 || ^3.0.0", "phpunit/phpunit": "^10.3"}, "type": "library", "autoload": {"psr-4": {"Carbon\\Doctrine\\": "src/Carbon/Doctrine/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KyleKatarn", "email": "<EMAIL>"}], "description": "Types to use Carbon in Doctrine", "keywords": ["carbon", "date", "datetime", "doctrine", "time"], "support": {"issues": "https://github.com/CarbonPHP/carbon-doctrine-types/issues", "source": "https://github.com/CarbonPHP/carbon-doctrine-types/tree/2.1.0"}, "funding": [{"url": "https://github.com/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2023-12-11T17:09:12+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "crwlr/query-string", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/crwlrsoft/query-string.git", "reference": "4e71cae0c942fe1ce2ae22506bd46ecdfa5b4f0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/crwlrsoft/query-string/zipball/4e71cae0c942fe1ce2ae22506bd46ecdfa5b4f0e", "reference": "4e71cae0c942fe1ce2ae22506bd46ecdfa5b4f0e", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.8", "pestphp/pest": "^1.21", "phpstan/phpstan": "^1.7"}, "type": "library", "autoload": {"psr-4": {"Crwlr\\QueryString\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.otsch.codes", "role": "Developer"}], "description": "A library for convenient handling of query strings used in HTTP requests.", "homepage": "https://www.crwlr.software/packages/query-string", "keywords": ["crwlr", "http", "query", "query string", "query-string", "request", "string"], "support": {"docs": "https://www.crwlr.software/packages/query-string", "issues": "https://github.com/crwlrsoft/query-string/issues", "source": "https://github.com/crwlrsoft/query-string"}, "time": "2023-04-12T21:31:58+00:00"}, {"name": "dflydev/dot-access-data", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/dflydev/dflydev-dot-access-data.git", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dflydev/dflydev-dot-access-data/zipball/a23a2bf4f31d3518f3ecb38660c95715dfead60f", "reference": "a23a2bf4f31d3518f3ecb38660c95715dfead60f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.3", "scrutinizer/ocular": "1.6.0", "squizlabs/php_codesniffer": "^3.5", "vimeo/psalm": "^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Dflydev\\DotAccessData\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Dragonfly Development Inc.", "email": "<EMAIL>", "homepage": "http://dflydev.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/cfrutos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com"}], "description": "Given a deep data structure, access data by dot notation.", "homepage": "https://github.com/dflydev/dflydev-dot-access-data", "keywords": ["access", "data", "dot", "notation"], "support": {"issues": "https://github.com/dflydev/dflydev-dot-access-data/issues", "source": "https://github.com/dflydev/dflydev-dot-access-data/tree/v3.0.3"}, "time": "2024-07-08T12:26:09+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "3.9.5", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "4a4e2eed3134036ee36a147ee0dac037dfa17868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/4a4e2eed3134036ee36a147ee0dac037dfa17868", "reference": "4a4e2eed3134036ee36a147ee0dac037dfa17868", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "13.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "2.1.17", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "9.6.23", "slevomat/coding-standard": "8.16.2", "squizlabs/php_codesniffer": "3.13.1", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.9.5"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2025-06-15T22:40:05+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/inflector", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/5817d0659c5b50c9b950feb9af7b9668e2c436bc", "reference": "5817d0659c5b50c9b950feb9af7b9668e2c436bc", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^11.0", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^8.5 || ^9.5", "vimeo/psalm": "^4.25 || ^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.10"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2024-02-18T20:23:39+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "doctrine/sql-formatter", "version": "1.5.2", "source": {"type": "git", "url": "https://github.com/doctrine/sql-formatter.git", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/sql-formatter/zipball/d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "reference": "d6d00aba6fd2957fe5216fe2b7673e9985db20c8", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "ergebnis/phpunit-slow-test-detector": "^2.14", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5"}, "bin": ["bin/sql-formatter"], "type": "library", "autoload": {"psr-4": {"Doctrine\\SqlFormatter\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/doctrine/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/doctrine/sql-formatter/issues", "source": "https://github.com/doctrine/sql-formatter/tree/1.5.2"}, "time": "2025-01-24T11:45:48+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v3.4.0", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "8c784d071debd117328803d86b2097615b457500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/8c784d071debd117328803d86b2097615b457500", "reference": "8c784d071debd117328803d86b2097615b457500", "shasum": ""}, "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "replace": {"mtdowling/cron-expression": "^1.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v3.4.0"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2024-10-09T13:47:03+00:00"}, {"name": "egulias/email-validator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "reference": "d42c8731f0624ad6bdc8d3e5e9a4524f68801cfa", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.4"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2025-03-06T22:45:56+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.18.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "cb56001e54359df7ae76dc522d08845dc741621b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/cb56001e54359df7ae76dc522d08845dc741621b", "reference": "cb56001e54359df7ae76dc522d08845dc741621b", "shasum": ""}, "require": {"php": "~5.6.0 || ~7.0.0 || ~7.1.0 || ~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cerdic/css-tidy": "^1.7 || ^2.0", "simpletest/simpletest": "dev-master"}, "suggest": {"cerdic/css-tidy": "If you want to use the filter 'Filter.ExtractStyleBlocks'.", "ext-bcmath": "Used for unit conversion and imagecrash protection", "ext-iconv": "Converts text to and from non-UTF-8 encodings", "ext-tidy": "Used for pretty-printing HTML"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/v4.18.0"}, "time": "2024-11-01T03:51:45+00:00"}, {"name": "firebase/php-jwt", "version": "v6.11.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "reference": "d1e91ecf8c598d073d0995afa8cd5c75c6e19e66", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.11.1"}, "time": "2025-04-09T20:32:01+00:00"}, {"name": "fruitcake/php-cors", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/fruitcake/php-cors.git", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fruitcake/php-cors/zipball/3d158f36e7875e2f040f37bc0573956240a5a38b", "reference": "3d158f36e7875e2f040f37bc0573956240a5a38b", "shasum": ""}, "require": {"php": "^7.4|^8.0", "symfony/http-foundation": "^4.4|^5.4|^6|^7"}, "require-dev": {"phpstan/phpstan": "^1.4", "phpunit/phpunit": "^9", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Fruitcake\\Cors\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Fruitcake", "homepage": "https://fruitcake.nl"}, {"name": "Barryvdh", "email": "<EMAIL>"}], "description": "Cross-origin resource sharing library for the Symfony HttpFoundation", "homepage": "https://github.com/fruitcake/php-cors", "keywords": ["cors", "laravel", "symfony"], "support": {"issues": "https://github.com/fruitcake/php-cors/issues", "source": "https://github.com/fruitcake/php-cors/tree/v1.3.0"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2023-10-12T05:21:21+00:00"}, {"name": "ghazniali95/react-with-laravel-blade", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/ghazniali95/react-with-laravel-blade.git", "reference": "74a39d557dc251e5eb54e82681b1ad0f1a5d60ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ghazniali95/react-with-laravel-blade/zipball/74a39d557dc251e5eb54e82681b1ad0f1a5d60ef", "reference": "74a39d557dc251e5eb54e82681b1ad0f1a5d60ef", "shasum": ""}, "require": {"php": "^8.0"}, "type": "laravel-package", "autoload": {"psr-4": {"Ghazniali95\\ReactWithLaravelBlade\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "React component laravel vite helper.", "keywords": ["laravel", "laravel-vite-helper", "react", "react-components-laravel-helper", "react-helper-with-laravel", "react-laravel"], "support": {"issues": "https://github.com/ghazniali95/react-with-laravel-blade/issues", "source": "https://github.com/ghazniali95/react-with-laravel-blade/tree/v1.0.0"}, "time": "2024-03-29T09:05:04+00:00"}, {"name": "ghazniali95/shopifyconnector", "version": "dev-main", "source": {"type": "git", "url": "https://github.com/ghazniali95/ShopifyConnector.git", "reference": "5b850c6393a8b7aa4f9737f8f218a362762bcbb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ghazniali95/ShopifyConnector/zipball/5b850c6393a8b7aa4f9737f8f218a362762bcbb4", "reference": "5b850c6393a8b7aa4f9737f8f218a362762bcbb4", "shasum": ""}, "require": {"doctrine/inflector": "^2.0", "ext-ctype": "*", "ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "firebase/php-jwt": "^5.2 || ^6.2", "guzzlehttp/guzzle": "^7.4.4", "guzzlehttp/psr7": "^2.0", "php": "~8.0.0 || ~8.1.0 || ~8.2.0", "phpunit/phpunit": "^9.0", "psr/http-client": "^1.0", "psr/http-message": "^1.0", "psr/log": "^1.1 || ^2.0 || ^3.0", "ramsey/uuid": "^4.1"}, "require-dev": {"ergebnis/composer-normalize": "^2.30", "maglnet/composer-require-checker": "^3.0 || ^4.0", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^9.0", "squizlabs/php_codesniffer": "^3.6"}, "default-branch": true, "type": "library", "extra": {"laravel": {"aliases": {"ShopifyService": "Ghazniali95\\ShopifyConnector\\App\\Facades\\ShopifyService"}, "providers": ["Ghazniali95\\ShopifyConnector\\App\\Providers\\ShopifyConnectorProvider"]}}, "autoload": {"psr-4": {"Ghazniali95\\ShopifyConnector\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Connects and Performs operations for shopify.", "keywords": ["Storefront API", "admin api", "app", "graphql", "jwt", "node", "rest", "shopify", "webhook"], "support": {"issues": "https://github.com/ghazniali95/ShopifyConnector/issues", "source": "https://github.com/ghazniali95/ShopifyConnector/tree/main"}, "time": "2024-08-27T12:09:53+00:00"}, {"name": "graham-campbell/result-type", "version": "v1.1.3", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Result-Type.git", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Result-Type/zipball/3ba905c11371512af9d9bdd27d99b782216b6945", "reference": "3ba905c11371512af9d9bdd27d99b782216b6945", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3"}, "require-dev": {"phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "autoload": {"psr-4": {"GrahamCampbell\\ResultType\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "An Implementation Of The Result Type", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Result Type", "Result-Type", "result"], "support": {"issues": "https://github.com/GrahamCampbell/Result-Type/issues", "source": "https://github.com/GrahamCampbell/Result-Type/tree/v1.1.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/result-type", "type": "tidelift"}], "time": "2024-07-20T21:45:45+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "guzzlehttp/uri-template", "version": "v1.0.4", "source": {"type": "git", "url": "https://github.com/guzzle/uri-template.git", "reference": "30e286560c137526eccd4ce21b2de477ab0676d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/uri-template/zipball/30e286560c137526eccd4ce21b2de477ab0676d2", "reference": "30e286560c137526eccd4ce21b2de477ab0676d2", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "uri-template/tests": "1.0.0"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\UriTemplate\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}], "description": "A polyfill class for uri_template of PHP", "keywords": ["guzzlehttp", "uri-template"], "support": {"issues": "https://github.com/guzzle/uri-template/issues", "source": "https://github.com/guzzle/uri-template/tree/v1.0.4"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/uri-template", "type": "tidelift"}], "time": "2025-02-03T10:55:03+00:00"}, {"name": "hollodotme/fast-cgi-client", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/hollodotme/fast-cgi-client.git", "reference": "062182d4eda73c161cc2839783acc83096ec0f37"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hollodotme/fast-cgi-client/zipball/062182d4eda73c161cc2839783acc83096ec0f37", "reference": "062182d4eda73c161cc2839783acc83096ec0f37", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"ext-xdebug": ">=2.6.0"}, "type": "library", "autoload": {"psr-4": {"hollodotme\\FastCGI\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHP fast CGI client to send requests (a)synchronously to PHP-FPM.", "keywords": ["Socket", "async", "fastcgi", "php-fpm"], "support": {"issues": "https://github.com/hollodotme/fast-cgi-client/issues", "source": "https://github.com/hollodotme/fast-cgi-client/tree/v3.1.7"}, "time": "2021-12-07T10:10:20+00:00"}, {"name": "inertiajs/inertia-laravel", "version": "v1.3.3", "source": {"type": "git", "url": "https://github.com/inertiajs/inertia-laravel.git", "reference": "7f5eecd6fd8c82e78435a52f84bdb0113297c280"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/inertiajs/inertia-laravel/zipball/7f5eecd6fd8c82e78435a52f84bdb0113297c280", "reference": "7f5eecd6fd8c82e78435a52f84bdb0113297c280", "shasum": ""}, "require": {"ext-json": "*", "laravel/framework": "^8.74|^9.0|^10.0|^11.0|^12.0", "php": "^7.3|~8.0.0|~8.1.0|~8.2.0|~8.3.0|~8.4.0", "symfony/console": "^5.3|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench": "^6.45|^7.44|^8.25|^9.3|^10.0", "phpunit/phpunit": "^8.0|^9.5.8|^10.4|^11.5"}, "suggest": {"ext-pcntl": "Recommended when running the Inertia SSR server via the `inertia:start-ssr` artisan command."}, "type": "library", "extra": {"laravel": {"providers": ["Inertia\\ServiceProvider"]}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"files": ["./helpers.php"], "psr-4": {"Inertia\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://reinink.ca"}], "description": "The Laravel adapter for Inertia.js.", "keywords": ["inertia", "laravel"], "support": {"issues": "https://github.com/inertiajs/inertia-laravel/issues", "source": "https://github.com/inertiajs/inertia-laravel/tree/v1.3.3"}, "time": "2025-06-06T14:51:50+00:00"}, {"name": "josiasmontag/laravel-recaptchav3", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/josiasmontag/laravel-recaptchav3.git", "reference": "08548b818223a20fc7db04a8d060758f8efc4ef5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/josiasmontag/laravel-recaptchav3/zipball/08548b818223a20fc7db04a8d060758f8efc4ef5", "reference": "08548b818223a20fc7db04a8d060758f8efc4ef5", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.2|^7.0", "illuminate/container": "~5.7.0|~5.8.0|^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/http": "~5.7.0|~5.8.0|^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/support": "~5.7.0|~5.8.0|^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "php": ">=7.1.0"}, "require-dev": {"mockery/mockery": "^1.2", "orchestra/testbench": "~3.7.0|~3.8.0|^4.0|^5.0|^6.0|^7.0|^8.0|^9.0|^10.0", "phpunit/phpunit": "6.2|^7.0|^8.0|^9.5.10|^10.5|^11.5.3"}, "type": "library", "extra": {"laravel": {"aliases": {"RecaptchaV3": "Lunaweb\\RecaptchaV3\\Facades\\RecaptchaV3"}, "providers": ["Lunaweb\\RecaptchaV3\\Providers\\RecaptchaV3ServiceProvider"]}}, "autoload": {"psr-4": {"Lunaweb\\RecaptchaV3\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Recaptcha V3 for Laravel package", "homepage": "https://github.com/josiasmontag/laravel-recaptchav3", "keywords": ["<PERSON><PERSON>a", "laravel", "php", "recaptcha"], "support": {"issues": "https://github.com/josiasmontag/laravel-recaptchav3/issues", "source": "https://github.com/josiasmontag/laravel-recaptchav3/tree/1.0.4"}, "time": "2025-02-25T08:00:22+00:00"}, {"name": "laminas/laminas-diactoros", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "b068eac123f21c0e592de41deeb7403b88e0a89f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/b068eac123f21c0e592de41deeb7403b88e0a89f", "reference": "b068eac123f21c0e592de41deeb7403b88e0a89f", "shasum": ""}, "require": {"php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0", "psr/http-factory": "^1.1", "psr/http-message": "^1.1 || ^2.0"}, "conflict": {"amphp/amp": "<2.6.4"}, "provide": {"psr/http-factory-implementation": "^1.0", "psr/http-message-implementation": "^1.1 || ^2.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^2.2.0", "laminas/laminas-coding-standard": "~3.0.0", "php-http/psr7-integration-tests": "^1.4.0", "phpunit/phpunit": "^10.5.36", "psalm/plugin-phpunit": "^0.19.0", "vimeo/psalm": "^5.26.1"}, "type": "library", "extra": {"laminas": {"module": "Laminas\\Diactoros", "config-provider": "Laminas\\Diactoros\\ConfigProvider"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2025-05-05T16:03:34+00:00"}, {"name": "laravel/cashier", "version": "v15.4.3", "source": {"type": "git", "url": "https://github.com/laravel/cashier-stripe.git", "reference": "a934a85540f21c60f55e47c226f8ef9e8f201384"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/cashier-stripe/zipball/a934a85540f21c60f55e47c226f8ef9e8f201384", "reference": "a934a85540f21c60f55e47c226f8ef9e8f201384", "shasum": ""}, "require": {"ext-json": "*", "illuminate/console": "^10.0|^11.0", "illuminate/contracts": "^10.0|^11.0", "illuminate/database": "^10.0|^11.0", "illuminate/http": "^10.0|^11.0", "illuminate/log": "^10.0|^11.0", "illuminate/notifications": "^10.0|^11.0", "illuminate/pagination": "^10.0|^11.0", "illuminate/routing": "^10.0|^11.0", "illuminate/support": "^10.0|^11.0", "illuminate/view": "^10.0|^11.0", "moneyphp/money": "^4.0", "nesbot/carbon": "^2.0|^3.0", "php": "^8.1", "stripe/stripe-php": "^13.0", "symfony/console": "^6.0|^7.0", "symfony/http-kernel": "^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.22.1"}, "require-dev": {"dompdf/dompdf": "^2.0", "mockery/mockery": "^1.0", "orchestra/testbench": "^8.18|^9.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.4"}, "suggest": {"dompdf/dompdf": "Required when generating and downloading invoice PDF's using Dompdf (^1.0.1|^2.0).", "ext-intl": "Allows for more locales besides the default \"en\" when formatting money values."}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Cashier\\CashierServiceProvider"]}, "branch-alias": {"dev-master": "15.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Cashier\\": "src/", "Laravel\\Cashier\\Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Cashier provides an expressive, fluent interface to Stripe's subscription billing services.", "keywords": ["billing", "laravel", "stripe"], "support": {"issues": "https://github.com/laravel/cashier/issues", "source": "https://github.com/laravel/cashier"}, "time": "2024-09-03T14:51:03+00:00"}, {"name": "laravel/framework", "version": "v10.48.29", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "8f7f9247cb8aad1a769d6b9815a6623d89b46b47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/8f7f9247cb8aad1a769d6b9815a6623d89b46b47", "reference": "8f7f9247cb8aad1a769d6b9815a6623d89b46b47", "shasum": ""}, "require": {"brick/math": "^0.9.3|^0.10.2|^0.11|^0.12", "composer-runtime-api": "^2.2", "doctrine/inflector": "^2.0.5", "dragonmantank/cron-expression": "^3.3.2", "egulias/email-validator": "^3.2.1|^4.0", "ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-session": "*", "ext-tokenizer": "*", "fruitcake/php-cors": "^1.2", "guzzlehttp/uri-template": "^1.0", "laravel/prompts": "^0.1.9", "laravel/serializable-closure": "^1.3", "league/commonmark": "^2.2.1", "league/flysystem": "^3.8.0", "monolog/monolog": "^3.0", "nesbot/carbon": "^2.67", "nunomaduro/termwind": "^1.13", "php": "^8.1", "psr/container": "^1.1.1|^2.0.1", "psr/log": "^1.0|^2.0|^3.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "ramsey/uuid": "^4.7", "symfony/console": "^6.2", "symfony/error-handler": "^6.2", "symfony/finder": "^6.2", "symfony/http-foundation": "^6.4", "symfony/http-kernel": "^6.2", "symfony/mailer": "^6.2", "symfony/mime": "^6.2", "symfony/process": "^6.2", "symfony/routing": "^6.2", "symfony/uid": "^6.2", "symfony/var-dumper": "^6.2", "tijsverkoyen/css-to-inline-styles": "^2.2.5", "vlucas/phpdotenv": "^5.4.1", "voku/portable-ascii": "^2.0"}, "conflict": {"carbonphp/carbon-doctrine-types": ">=3.0", "doctrine/dbal": ">=4.0", "mockery/mockery": "1.6.8", "phpunit/phpunit": ">=11.0.0", "tightenco/collect": "<5.5.33"}, "provide": {"psr/container-implementation": "1.1|2.0", "psr/simple-cache-implementation": "1.0|2.0|3.0"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/collections": "self.version", "illuminate/conditionable": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/macroable": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/process": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/testing": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"ably/ably-php": "^1.0", "aws/aws-sdk-php": "^3.235.5", "doctrine/dbal": "^3.5.1", "ext-gmp": "*", "fakerphp/faker": "^1.21", "guzzlehttp/guzzle": "^7.5", "league/flysystem-aws-s3-v3": "^3.0", "league/flysystem-ftp": "^3.0", "league/flysystem-path-prefixing": "^3.3", "league/flysystem-read-only": "^3.3", "league/flysystem-sftp-v3": "^3.0", "mockery/mockery": "^1.5.1", "nyholm/psr7": "^1.2", "orchestra/testbench-core": "^8.23.4", "pda/pheanstalk": "^4.0", "phpstan/phpstan": "~1.11.11", "phpunit/phpunit": "^10.0.7", "predis/predis": "^2.0.2", "symfony/cache": "^6.2", "symfony/http-client": "^6.2.4", "symfony/psr-http-message-bridge": "^2.0"}, "suggest": {"ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage, and SES mail driver (^3.235.5).", "brianium/paratest": "Required to run tests in parallel (^6.0).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^3.5.1).", "ext-apcu": "Required to use the APC cache driver.", "ext-fileinfo": "Required to use the Filesystem class.", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker and console signal trapping.", "ext-pdo": "Required to use all database features.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.14.3).", "guzzlehttp/guzzle": "Required to use the HTTP Client and the ping methods on schedules (^7.5).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^3.0).", "league/flysystem-ftp": "Required to use the Flysystem FTP driver (^3.0).", "league/flysystem-path-prefixing": "Required to use the scoped driver (^3.3).", "league/flysystem-read-only": "Required to use read-only disks (^3.3)", "league/flysystem-sftp-v3": "Required to use the Flysystem SFTP driver (^3.0).", "mockery/mockery": "Required to use mocking (^1.5.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "phpunit/phpunit": "Required to use assertions and run tests (^9.5.8|^10.0.7).", "predis/predis": "Required to use the predis connector (^2.0.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^6.0|^7.0).", "symfony/cache": "Required to PSR-6 cache bridge (^6.2).", "symfony/filesystem": "Required to enable support for relative symbolic links (^6.2).", "symfony/http-client": "Required to enable support for the Symfony API mail transports (^6.2).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^6.2).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^6.2).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^2.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "10.x-dev"}}, "autoload": {"files": ["src/Illuminate/Collections/helpers.php", "src/Illuminate/Events/functions.php", "src/Illuminate/Filesystem/functions.php", "src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/", "Illuminate\\Support\\": ["src/Illuminate/Macroable/", "src/Illuminate/Collections/", "src/Illuminate/Conditionable/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2025-03-12T14:42:01+00:00"}, {"name": "laravel/octane", "version": "v2.10.0", "source": {"type": "git", "url": "https://github.com/laravel/octane.git", "reference": "62bf04dcb471e7d91c8aeda9b4e7376a75000ee5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/octane/zipball/62bf04dcb471e7d91c8aeda9b4e7376a75000ee5", "reference": "62bf04dcb471e7d91c8aeda9b4e7376a75000ee5", "shasum": ""}, "require": {"laminas/laminas-diactoros": "^3.0", "laravel/framework": "^10.10.1|^11.0|^12.0", "laravel/prompts": "^0.1.24|^0.2.0|^0.3.0", "laravel/serializable-closure": "^1.3|^2.0", "nesbot/carbon": "^2.66.0|^3.0", "php": "^8.1.0", "symfony/console": "^6.0|^7.0", "symfony/psr-http-message-bridge": "^2.2.0|^6.4|^7.0"}, "conflict": {"spiral/roadrunner": "<2023.1.0", "spiral/roadrunner-cli": "<2.6.0", "spiral/roadrunner-http": "<3.3.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.6.1", "inertiajs/inertia-laravel": "^1.3.2|^2.0", "laravel/scout": "^10.2.1", "laravel/socialite": "^5.6.1", "livewire/livewire": "^2.12.3|^3.0", "mockery/mockery": "^1.5.1", "nunomaduro/collision": "^6.4.0|^7.5.2|^8.0", "orchestra/testbench": "^8.21|^9.0|^10.0", "phpstan/phpstan": "^2.1.7", "phpunit/phpunit": "^10.4|^11.5", "spiral/roadrunner-cli": "^2.6.0", "spiral/roadrunner-http": "^3.3.0"}, "bin": ["bin/roadrunner-worker", "bin/swoole-server"], "type": "library", "extra": {"laravel": {"aliases": {"Octane": "Laravel\\Octane\\Facades\\Octane"}, "providers": ["Laravel\\Octane\\OctaneServiceProvider"]}, "branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Octane\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharge your Laravel application's performance.", "keywords": ["frankenphp", "laravel", "octane", "roadrunner", "swoole"], "support": {"issues": "https://github.com/laravel/octane/issues", "source": "https://github.com/laravel/octane"}, "time": "2025-06-16T13:44:31+00:00"}, {"name": "laravel/prompts", "version": "v0.1.25", "source": {"type": "git", "url": "https://github.com/laravel/prompts.git", "reference": "7b4029a84c37cb2725fc7f011586e2997040bc95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/prompts/zipball/7b4029a84c37cb2725fc7f011586e2997040bc95", "reference": "7b4029a84c37cb2725fc7f011586e2997040bc95", "shasum": ""}, "require": {"ext-mbstring": "*", "illuminate/collections": "^10.0|^11.0", "php": "^8.1", "symfony/console": "^6.2|^7.0"}, "conflict": {"illuminate/console": ">=10.17.0 <10.25.0", "laravel/framework": ">=10.17.0 <10.25.0"}, "require-dev": {"mockery/mockery": "^1.5", "pestphp/pest": "^2.3", "phpstan/phpstan": "^1.11", "phpstan/phpstan-mockery": "^1.1"}, "suggest": {"ext-pcntl": "Required for the spinner to be animated."}, "type": "library", "extra": {"branch-alias": {"dev-main": "0.1.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Laravel\\Prompts\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Add beautiful and user-friendly forms to your command-line applications.", "support": {"issues": "https://github.com/laravel/prompts/issues", "source": "https://github.com/laravel/prompts/tree/v0.1.25"}, "time": "2024-08-12T22:06:33+00:00"}, {"name": "laravel/pulse", "version": "v1.4.2", "source": {"type": "git", "url": "https://github.com/laravel/pulse.git", "reference": "73c349777e09893a68d747bb6ae87e54eb5e5aec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/pulse/zipball/73c349777e09893a68d747bb6ae87e54eb5e5aec", "reference": "73c349777e09893a68d747bb6ae87e54eb5e5aec", "shasum": ""}, "require": {"doctrine/sql-formatter": "^1.4.1", "guzzlehttp/promises": "^1.0|^2.0", "illuminate/auth": "^10.48.4|^11.0.8|^12.0", "illuminate/cache": "^10.48.4|^11.0.8|^12.0", "illuminate/config": "^10.48.4|^11.0.8|^12.0", "illuminate/console": "^10.48.4|^11.0.8|^12.0", "illuminate/contracts": "^10.48.4|^11.0.8|^12.0", "illuminate/database": "^10.48.4|^11.0.8|^12.0", "illuminate/events": "^10.48.4|^11.0.8|^12.0", "illuminate/http": "^10.48.4|^11.0.8|^12.0", "illuminate/queue": "^10.48.4|^11.0.8|^12.0", "illuminate/redis": "^10.48.4|^11.0.8|^12.0", "illuminate/routing": "^10.48.4|^11.0.8|^12.0", "illuminate/support": "^10.48.4|^11.0.8|^12.0", "illuminate/view": "^10.48.4|^11.0.8|^12.0", "livewire/livewire": "^3.4.9", "nesbot/carbon": "^2.67|^3.0", "php": "^8.1", "symfony/console": "^6.0|^7.0"}, "conflict": {"nunomaduro/collision": "<7.7.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.7", "mockery/mockery": "^1.0", "orchestra/testbench": "^8.23.1|^9.0|^10.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.2", "phpstan/phpstan": "^1.12.21", "predis/predis": "^1.0|^2.0"}, "type": "library", "extra": {"laravel": {"aliases": {"Pulse": "Laravel\\Pulse\\Facades\\Pulse"}, "providers": ["Laravel\\Pulse\\PulseServiceProvider"]}, "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Pulse\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Pulse is a real-time application performance monitoring tool and dashboard for your Laravel application.", "homepage": "https://github.com/laravel/pulse", "keywords": ["laravel"], "support": {"issues": "https://github.com/laravel/pulse/issues", "source": "https://github.com/laravel/pulse"}, "time": "2025-05-19T13:12:28+00:00"}, {"name": "laravel/sanctum", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/laravel/sanctum.git", "reference": "8c104366459739f3ada0e994bcd3e6fd681ce3d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sanctum/zipball/8c104366459739f3ada0e994bcd3e6fd681ce3d5", "reference": "8c104366459739f3ada0e994bcd3e6fd681ce3d5", "shasum": ""}, "require": {"ext-json": "*", "illuminate/console": "^9.21|^10.0", "illuminate/contracts": "^9.21|^10.0", "illuminate/database": "^9.21|^10.0", "illuminate/support": "^9.21|^10.0", "php": "^8.0.2"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^7.28.2|^8.8.3", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.6"}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Sanctum\\SanctumServiceProvider"]}, "branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Sanctum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel Sanctum provides a featherweight authentication system for SPAs and simple APIs.", "keywords": ["auth", "laravel", "sanctum"], "support": {"issues": "https://github.com/laravel/sanctum/issues", "source": "https://github.com/laravel/sanctum"}, "time": "2023-12-19T18:44:48+00:00"}, {"name": "laravel/serializable-closure", "version": "v1.3.7", "source": {"type": "git", "url": "https://github.com/laravel/serializable-closure.git", "reference": "4f48ade902b94323ca3be7646db16209ec76be3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/serializable-closure/zipball/4f48ade902b94323ca3be7646db16209ec76be3d", "reference": "4f48ade902b94323ca3be7646db16209ec76be3d", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"illuminate/support": "^8.0|^9.0|^10.0|^11.0", "nesbot/carbon": "^2.61|^3.0", "pestphp/pest": "^1.21.3", "phpstan/phpstan": "^1.8.2", "symfony/var-dumper": "^5.4.11|^6.2.0|^7.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Laravel\\SerializableClosure\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Laravel Serializable Closure provides an easy and secure way to serialize closures in PHP.", "keywords": ["closure", "laravel", "serializable"], "support": {"issues": "https://github.com/laravel/serializable-closure/issues", "source": "https://github.com/laravel/serializable-closure"}, "time": "2024-11-14T18:34:49+00:00"}, {"name": "laravel/slack-notification-channel", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/laravel/slack-notification-channel.git", "reference": "e0d4be5e01d443a69fa89f0a4cac6bd2eda2be8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/slack-notification-channel/zipball/e0d4be5e01d443a69fa89f0a4cac6bd2eda2be8f", "reference": "e0d4be5e01d443a69fa89f0a4cac6bd2eda2be8f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0|^7.0", "illuminate/notifications": "~5.8.0|^6.0|^7.0|^8.0|^9.0|^10.0", "php": "^7.1.3|^8.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Illuminate\\Notifications\\SlackChannelServiceProvider"]}, "branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Illuminate\\Notifications\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Slack Notification Channel for laravel.", "keywords": ["laravel", "notifications", "slack"], "support": {"issues": "https://github.com/laravel/slack-notification-channel/issues", "source": "https://github.com/laravel/slack-notification-channel/tree/v2.5.0"}, "time": "2023-01-12T16:21:26+00:00"}, {"name": "laravel/socialite", "version": "v5.21.0", "source": {"type": "git", "url": "https://github.com/laravel/socialite.git", "reference": "d83639499ad14985c9a6a9713b70073300ce998d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/socialite/zipball/d83639499ad14985c9a6a9713b70073300ce998d", "reference": "d83639499ad14985c9a6a9713b70073300ce998d", "shasum": ""}, "require": {"ext-json": "*", "firebase/php-jwt": "^6.4", "guzzlehttp/guzzle": "^6.0|^7.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/http": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "league/oauth1-client": "^1.11", "php": "^7.2|^8.0", "phpseclib/phpseclib": "^3.0"}, "require-dev": {"mockery/mockery": "^1.0", "orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0|^9.0|^10.0", "phpstan/phpstan": "^1.12.23", "phpunit/phpunit": "^8.0|^9.3|^10.4|^11.5"}, "type": "library", "extra": {"laravel": {"aliases": {"Socialite": "Laravel\\Socialite\\Facades\\Socialite"}, "providers": ["Laravel\\Socialite\\SocialiteServiceProvider"]}, "branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Socialite\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel wrapper around OAuth 1 & OAuth 2 libraries.", "homepage": "https://laravel.com", "keywords": ["laravel", "o<PERSON>h"], "support": {"issues": "https://github.com/laravel/socialite/issues", "source": "https://github.com/laravel/socialite"}, "time": "2025-05-19T12:56:37+00:00"}, {"name": "laravel/tinker", "version": "v2.10.1", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "22177cc71807d38f2810c6204d8f7183d88a57d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/22177cc71807d38f2810c6204d8f7183d88a57d3", "reference": "22177cc71807d38f2810c6204d8f7183d88a57d3", "shasum": ""}, "require": {"illuminate/console": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/contracts": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "php": "^7.2.5|^8.0", "psy/psysh": "^0.11.1|^0.12.0", "symfony/var-dumper": "^4.3.4|^5.0|^6.0|^7.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8.5.8|^9.3.3|^10.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0)."}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v2.10.1"}, "time": "2025-01-27T14:24:01+00:00"}, {"name": "laravel/ui", "version": "v4.6.1", "source": {"type": "git", "url": "https://github.com/laravel/ui.git", "reference": "7d6ffa38d79f19c9b3e70a751a9af845e8f41d88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/ui/zipball/7d6ffa38d79f19c9b3e70a751a9af845e8f41d88", "reference": "7d6ffa38d79f19c9b3e70a751a9af845e8f41d88", "shasum": ""}, "require": {"illuminate/console": "^9.21|^10.0|^11.0|^12.0", "illuminate/filesystem": "^9.21|^10.0|^11.0|^12.0", "illuminate/support": "^9.21|^10.0|^11.0|^12.0", "illuminate/validation": "^9.21|^10.0|^11.0|^12.0", "php": "^8.0", "symfony/console": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^7.35|^8.15|^9.0|^10.0", "phpunit/phpunit": "^9.3|^10.4|^11.5"}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Ui\\UiServiceProvider"]}, "branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Ui\\": "src/", "Illuminate\\Foundation\\Auth\\": "auth-backend/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Laravel UI utilities and presets.", "keywords": ["laravel", "ui"], "support": {"source": "https://github.com/laravel/ui/tree/v4.6.1"}, "time": "2025-01-28T15:15:29+00:00"}, {"name": "league/commonmark", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "6fbb36d44824ed4091adbcf4c7d4a3923cdb3405"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/6fbb36d44824ed4091adbcf4c7d4a3923cdb3405", "reference": "6fbb36d44824ed4091adbcf4c7d4a3923cdb3405", "shasum": ""}, "require": {"ext-mbstring": "*", "league/config": "^1.1.1", "php": "^7.4 || ^8.0", "psr/event-dispatcher": "^1.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"cebe/markdown": "^1.0", "commonmark/cmark": "0.31.1", "commonmark/commonmark.js": "0.31.1", "composer/package-versions-deprecated": "^1.8", "embed/embed": "^4.4", "erusev/parsedown": "^1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "^1.4 || ^2.0", "nyholm/psr7": "^1.5", "phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.21 || ^10.5.9 || ^11.0.0", "scrutinizer/ocular": "^1.8.1", "symfony/finder": "^5.3 | ^6.0 | ^7.0", "symfony/process": "^5.4 | ^6.0 | ^7.0", "symfony/yaml": "^2.3 | ^3.0 | ^4.0 | ^5.0 | ^6.0 | ^7.0", "unleashedtech/php-coding-standard": "^3.1.1", "vimeo/psalm": "^4.24.0 || ^5.0.0"}, "suggest": {"symfony/yaml": "v2.3+ required if using the Front Matter extension"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.8-dev"}}, "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and GitHub-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "forum": "https://github.com/thephpleague/commonmark/discussions", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2025-05-05T12:20:28+00:00"}, {"name": "league/config", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/thephpleague/config.git", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/config/zipball/754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "reference": "754b3604fb2984c71f4af4a9cbe7b57f346ec1f3", "shasum": ""}, "require": {"dflydev/dot-access-data": "^3.0.1", "nette/schema": "^1.2", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.8.2", "phpunit/phpunit": "^9.5.5", "scrutinizer/ocular": "^1.8.1", "unleashedtech/php-coding-standard": "^3.1", "vimeo/psalm": "^4.7.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.2-dev"}}, "autoload": {"psr-4": {"League\\Config\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Define configuration arrays with strict schemas and access values with dot notation", "homepage": "https://config.thephpleague.com", "keywords": ["array", "config", "configuration", "dot", "dot-access", "nested", "schema"], "support": {"docs": "https://config.thephpleague.com/", "issues": "https://github.com/thephpleague/config/issues", "rss": "https://github.com/thephpleague/config/releases.atom", "source": "https://github.com/thephpleague/config"}, "funding": [{"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}], "time": "2022-12-11T20:36:23+00:00"}, {"name": "league/flysystem", "version": "3.30.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/2203e3151755d874bb2943649dae1eb8533ac93e", "reference": "2203e3151755d874bb2943649dae1eb8533ac93e", "shasum": ""}, "require": {"league/flysystem-local": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"async-aws/core": "<1.19.0", "async-aws/s3": "<1.14.0", "aws/aws-sdk-php": "3.209.31 || 3.210.0", "guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1", "phpseclib/phpseclib": "3.0.15", "symfony/http-client": "<5.2"}, "require-dev": {"async-aws/s3": "^1.5 || ^2.0", "async-aws/simple-s3": "^1.1 || ^2.0", "aws/aws-sdk-php": "^3.295.10", "composer/semver": "^3.0", "ext-fileinfo": "*", "ext-ftp": "*", "ext-mongodb": "^1.3|^2", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.5", "google/cloud-storage": "^1.23", "guzzlehttp/psr7": "^2.6", "microsoft/azure-storage-blob": "^1.1", "mongodb/mongodb": "^1.2|^2", "phpseclib/phpseclib": "^3.0.36", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.11|^10.0", "sabre/dav": "^4.6.0"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "File storage abstraction for PHP", "keywords": ["WebDAV", "aws", "cloud", "file", "files", "filesystem", "filesystems", "ftp", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/3.30.0"}, "time": "2025-06-25T13:29:59+00:00"}, {"name": "league/flysystem-aws-s3-v3", "version": "3.29.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-aws-s3-v3.git", "reference": "c6ff6d4606e48249b63f269eba7fabdb584e76a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-aws-s3-v3/zipball/c6ff6d4606e48249b63f269eba7fabdb584e76a9", "reference": "c6ff6d4606e48249b63f269eba7fabdb584e76a9", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.295.10", "league/flysystem": "^3.10.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "conflict": {"guzzlehttp/guzzle": "<7.0", "guzzlehttp/ringphp": "<1.1.1"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\AwsS3V3\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "AWS S3 filesystem adapter for Flysystem.", "keywords": ["Flysystem", "aws", "file", "files", "filesystem", "s3", "storage"], "support": {"source": "https://github.com/thephpleague/flysystem-aws-s3-v3/tree/3.29.0"}, "time": "2024-08-17T13:10:48+00:00"}, {"name": "league/flysystem-local", "version": "3.30.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-local.git", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-local/zipball/6691915f77c7fb69adfb87dcd550052dc184ee10", "reference": "6691915f77c7fb69adfb87dcd550052dc184ee10", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/flysystem": "^3.0.0", "league/mime-type-detection": "^1.0.0", "php": "^8.0.2"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Local\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Local filesystem adapter for Flysystem.", "keywords": ["Flysystem", "file", "files", "filesystem", "local"], "support": {"source": "https://github.com/thephpleague/flysystem-local/tree/3.30.0"}, "time": "2025-05-21T10:34:19+00:00"}, {"name": "league/mime-type-detection", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/2d6702ff215bf922936ccc1ad31007edc76451b9", "reference": "2d6702ff215bf922936ccc1ad31007edc76451b9", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3 || ^10.0"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.16.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2024-09-21T08:32:55+00:00"}, {"name": "league/oauth1-client", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth1-client.git", "reference": "f9c94b088837eb1aae1ad7c4f23eb65cc6993055"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth1-client/zipball/f9c94b088837eb1aae1ad7c4f23eb65cc6993055", "reference": "f9c94b088837eb1aae1ad7c4f23eb65cc6993055", "shasum": ""}, "require": {"ext-json": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "guzzlehttp/psr7": "^1.7|^2.0", "php": ">=7.1||>=8.0"}, "require-dev": {"ext-simplexml": "*", "friendsofphp/php-cs-fixer": "^2.17", "mockery/mockery": "^1.3.3", "phpstan/phpstan": "^0.12.42", "phpunit/phpunit": "^7.5||9.5"}, "suggest": {"ext-simplexml": "For decoding XML-based responses."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev", "dev-develop": "2.0-dev"}}, "autoload": {"psr-4": {"League\\OAuth1\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.webcomm.com.au", "role": "Developer"}], "description": "OAuth 1.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "bitbucket", "identity", "idp", "o<PERSON>h", "oauth1", "single sign on", "trello", "tumblr", "twitter"], "support": {"issues": "https://github.com/thephpleague/oauth1-client/issues", "source": "https://github.com/thephpleague/oauth1-client/tree/v1.11.0"}, "time": "2024-12-10T19:59:05+00:00"}, {"name": "livewire/livewire", "version": "v3.6.3", "source": {"type": "git", "url": "https://github.com/livewire/livewire.git", "reference": "56aa1bb63a46e06181c56fa64717a7287e19115e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/livewire/livewire/zipball/56aa1bb63a46e06181c56fa64717a7287e19115e", "reference": "56aa1bb63a46e06181c56fa64717a7287e19115e", "shasum": ""}, "require": {"illuminate/database": "^10.0|^11.0|^12.0", "illuminate/routing": "^10.0|^11.0|^12.0", "illuminate/support": "^10.0|^11.0|^12.0", "illuminate/validation": "^10.0|^11.0|^12.0", "laravel/prompts": "^0.1.24|^0.2|^0.3", "league/mime-type-detection": "^1.9", "php": "^8.1", "symfony/console": "^6.0|^7.0", "symfony/http-kernel": "^6.2|^7.0"}, "require-dev": {"calebporzio/sushi": "^2.1", "laravel/framework": "^10.15.0|^11.0|^12.0", "mockery/mockery": "^1.3.1", "orchestra/testbench": "^8.21.0|^9.0|^10.0", "orchestra/testbench-dusk": "^8.24|^9.1|^10.0", "phpunit/phpunit": "^10.4|^11.5", "psy/psysh": "^0.11.22|^0.12"}, "type": "library", "extra": {"laravel": {"aliases": {"Livewire": "Livewire\\Livewire"}, "providers": ["Livewire\\LivewireServiceProvider"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Livewire\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "caleb<PERSON><PERSON>@gmail.com"}], "description": "A front-end framework for Laravel.", "support": {"issues": "https://github.com/livewire/livewire/issues", "source": "https://github.com/livewire/livewire/tree/v3.6.3"}, "funding": [{"url": "https://github.com/livewire", "type": "github"}], "time": "2025-04-12T22:26:52+00:00"}, {"name": "maatwebsite/excel", "version": "3.1.64", "source": {"type": "git", "url": "https://github.com/SpartnerNL/Laravel-Excel.git", "reference": "e25d44a2d91da9179cd2d7fec952313548597a79"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SpartnerNL/Laravel-Excel/zipball/e25d44a2d91da9179cd2d7fec952313548597a79", "reference": "e25d44a2d91da9179cd2d7fec952313548597a79", "shasum": ""}, "require": {"composer/semver": "^3.3", "ext-json": "*", "illuminate/support": "5.8.*||^6.0||^7.0||^8.0||^9.0||^10.0||^11.0||^12.0", "php": "^7.0||^8.0", "phpoffice/phpspreadsheet": "^1.29.9", "psr/simple-cache": "^1.0||^2.0||^3.0"}, "require-dev": {"laravel/scout": "^7.0||^8.0||^9.0||^10.0", "orchestra/testbench": "^6.0||^7.0||^8.0||^9.0||^10.0", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"aliases": {"Excel": "Maatwebsite\\Excel\\Facades\\Excel"}, "providers": ["Maatwebsite\\Excel\\ExcelServiceProvider"]}}, "autoload": {"psr-4": {"Maatwebsite\\Excel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Supercharged Excel exports and imports in Laravel", "keywords": ["PHPExcel", "batch", "csv", "excel", "export", "import", "laravel", "php", "phpspreadsheet"], "support": {"issues": "https://github.com/SpartnerNL/Laravel-Excel/issues", "source": "https://github.com/SpartnerNL/Laravel-Excel/tree/3.1.64"}, "funding": [{"url": "https://laravel-excel.com/commercial-support", "type": "custom"}, {"url": "https://github.com/patrickbrouwers", "type": "github"}], "time": "2025-02-24T11:12:50+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/aeadcf5c412332eb426c0f9b4485f6accba2a99f", "reference": "aeadcf5c412332eb426c0f9b4485f6accba2a99f", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.2"}, "require-dev": {"brianium/paratest": "^7.7", "ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^11.0", "vimeo/psalm": "^6.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.2"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}], "time": "2025-01-27T12:07:53+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "moneyphp/money", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/moneyphp/money.git", "reference": "1a23f0e1b22e2c59ed5ed70cfbe4cbe696be9348"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/moneyphp/money/zipball/1a23f0e1b22e2c59ed5ed70cfbe4cbe696be9348", "reference": "1a23f0e1b22e2c59ed5ed70cfbe4cbe696be9348", "shasum": ""}, "require": {"ext-bcmath": "*", "ext-filter": "*", "ext-json": "*", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "require-dev": {"cache/taggable-cache": "^1.1.0", "doctrine/coding-standard": "^12.0", "doctrine/instantiator": "^1.5.0 || ^2.0", "ext-gmp": "*", "ext-intl": "*", "florianv/exchanger": "^2.8.1", "florianv/swap": "^4.3.0", "moneyphp/crypto-currencies": "^1.1.0", "moneyphp/iso-currencies": "^3.4", "php-http/message": "^1.16.0", "php-http/mock-client": "^1.6.0", "phpbench/phpbench": "^1.2.5", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1.9", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5.9", "psr/cache": "^1.0.1 || ^2.0 || ^3.0", "ticketswap/phpstan-error-formatter": "^1.1"}, "suggest": {"ext-gmp": "Calculate without integer limits", "ext-intl": "Format Money objects with intl", "florianv/exchanger": "Exchange rates library for PHP", "florianv/swap": "Exchange rates library for PHP", "psr/cache-implementation": "Used for Currency caching"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://verraes.net"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP implementation of Fowler's Money pattern", "homepage": "http://moneyphp.org", "keywords": ["Value Object", "money", "vo"], "support": {"issues": "https://github.com/moneyphp/money/issues", "source": "https://github.com/moneyphp/money/tree/v4.7.1"}, "time": "2025-06-06T07:12:38+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.8.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "reference": "a2a865e05d5f420b50cc2f85bb78d565db12a6bc", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.8.0"}, "time": "2024-09-04T18:46:31+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.1", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/1720ddd719e16cf0db4eb1c6eca108031636d46c", "reference": "1720ddd719e16cf0db4eb1c6eca108031636d46c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.1"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-04-29T12:36:36+00:00"}, {"name": "nesbot/carbon", "version": "2.73.0", "source": {"type": "git", "url": "https://github.com/CarbonPHP/carbon.git", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/CarbonPHP/carbon/zipball/9228ce90e1035ff2f0db84b40ec2e023ed802075", "reference": "9228ce90e1035ff2f0db84b40ec2e023ed802075", "shasum": ""}, "require": {"carbonphp/carbon-doctrine-types": "*", "ext-json": "*", "php": "^7.1.8 || ^8.0", "psr/clock": "^1.0", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation": "^3.4 || ^4.0 || ^5.0 || ^6.0"}, "provide": {"psr/clock-implementation": "1.0"}, "require-dev": {"doctrine/dbal": "^2.0 || ^3.1.4 || ^4.0", "doctrine/orm": "^2.7 || ^3.0", "friendsofphp/php-cs-fixer": "^3.0", "kylekatarnls/multi-tester": "^2.0", "ondrejmirtes/better-reflection": "<6", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.99 || ^1.7.14", "phpunit/php-file-iterator": "^2.0.5 || ^3.0.6", "phpunit/phpunit": "^7.5.20 || ^8.5.26 || ^9.5.20", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-2.x": "2.x-dev", "dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://markido.com"}, {"name": "kylekatarnls", "homepage": "https://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "https://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"docs": "https://carbon.nesbot.com/docs", "issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://github.com/sponsors/kylekatarnls", "type": "github"}, {"url": "https://opencollective.com/Carbon#sponsor", "type": "opencollective"}, {"url": "https://tidelift.com/subscription/pkg/packagist-nesbot-carbon?utm_source=packagist-nesbot-carbon&utm_medium=referral&utm_campaign=readme", "type": "tidelift"}], "time": "2025-01-08T20:10:23+00:00"}, {"name": "nette/schema", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "da801d52f0354f70a638673c4a0f04e16529431d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/da801d52f0354f70a638673c4a0f04e16529431d", "reference": "da801d52f0354f70a638673c4a0f04e16529431d", "shasum": ""}, "require": {"nette/utils": "^4.0", "php": "8.1 - 8.4"}, "require-dev": {"nette/tester": "^2.5.2", "phpstan/phpstan-nette": "^1.0", "tracy/tracy": "^2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.3.2"}, "time": "2024-10-06T23:10:23+00:00"}, {"name": "nette/utils", "version": "v4.0.7", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "e67c4061eb40b9c113b218214e42cb5a0dda28f2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/e67c4061eb40b9c113b218214e42cb5a0dda28f2", "reference": "e67c4061eb40b9c113b218214e42cb5a0dda28f2", "shasum": ""}, "require": {"php": "8.0 - 8.4"}, "conflict": {"nette/finder": "<3", "nette/schema": "<1.2.2"}, "require-dev": {"jetbrains/phpstorm-attributes": "dev-master", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v4.0.7"}, "time": "2025-06-03T04:55:08+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "nunomaduro/termwind", "version": "v1.17.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/termwind.git", "reference": "5369ef84d8142c1d87e4ec278711d4ece3cbf301"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/termwind/zipball/5369ef84d8142c1d87e4ec278711d4ece3cbf301", "reference": "5369ef84d8142c1d87e4ec278711d4ece3cbf301", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.1", "symfony/console": "^6.4.15"}, "require-dev": {"illuminate/console": "^10.48.24", "illuminate/support": "^10.48.24", "laravel/pint": "^1.18.2", "pestphp/pest": "^2.36.0", "pestphp/pest-plugin-mock": "2.0.0", "phpstan/phpstan": "^1.12.11", "phpstan/phpstan-strict-rules": "^1.6.1", "symfony/var-dumper": "^6.4.15", "thecodingmachine/phpstan-strict-rules": "^1.0.0"}, "type": "library", "extra": {"laravel": {"providers": ["Termwind\\Laravel\\TermwindServiceProvider"]}}, "autoload": {"files": ["src/Functions.php"], "psr-4": {"Termwind\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Its like Tailwind CSS, but for the console.", "keywords": ["cli", "console", "css", "package", "php", "style"], "support": {"issues": "https://github.com/nunomaduro/termwind/issues", "source": "https://github.com/nunomaduro/termwind/tree/v1.17.0"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://github.com/xiCO2k", "type": "github"}], "time": "2024-11-21T10:36:35+00:00"}, {"name": "nyholm/psr7", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/Nyholm/psr7.git", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/psr7/zipball/a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "reference": "a71f2b11690f4b24d099d6b16690a90ae14fc6f3", "shasum": ""}, "require": {"php": ">=7.2", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0", "psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "php-http/message-factory": "^1.0", "php-http/psr7-integration-tests": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.4", "symfony/error-handler": "^4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.8-dev"}}, "autoload": {"psr-4": {"Nyholm\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "marti<PERSON>@vanderven.se"}], "description": "A fast PHP7 implementation of PSR-7", "homepage": "https://tnyholm.se", "keywords": ["psr-17", "psr-7"], "support": {"issues": "https://github.com/Nyholm/psr7/issues", "source": "https://github.com/Nyholm/psr7/tree/1.8.2"}, "funding": [{"url": "https://github.com/Zegnat", "type": "github"}, {"url": "https://github.com/nyholm", "type": "github"}], "time": "2024-09-09T07:06:30+00:00"}, {"name": "openai-php/client", "version": "v0.10.3", "source": {"type": "git", "url": "https://github.com/openai-php/client.git", "reference": "4a565d145e0fb3ea1baba8fffe39d86c56b6dc2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openai-php/client/zipball/4a565d145e0fb3ea1baba8fffe39d86c56b6dc2c", "reference": "4a565d145e0fb3ea1baba8fffe39d86c56b6dc2c", "shasum": ""}, "require": {"php": "^8.1.0", "php-http/discovery": "^1.20.0", "php-http/multipart-stream-builder": "^1.4.2", "psr/http-client": "^1.0.3", "psr/http-client-implementation": "^1.0.1", "psr/http-factory-implementation": "*", "psr/http-message": "^1.1.0|^2.0.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.9.2", "guzzlehttp/psr7": "^2.7.0", "laravel/pint": "^1.18.1", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^7.11.0|^8.5.0", "pestphp/pest": "^2.36.0|^3.5.0", "pestphp/pest-plugin-arch": "^2.7|^3.0", "pestphp/pest-plugin-type-coverage": "^2.8.7|^3.1.0", "phpstan/phpstan": "^1.12.7", "symfony/var-dumper": "^6.4.11|^7.1.5"}, "type": "library", "autoload": {"files": ["src/OpenAI.php"], "psr-4": {"OpenAI\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>"}], "description": "OpenAI PHP is a supercharged PHP API client that allows you to interact with the Open AI API", "keywords": ["GPT-3", "api", "client", "codex", "dall-e", "language", "natural", "openai", "php", "processing", "sdk"], "support": {"issues": "https://github.com/openai-php/client/issues", "source": "https://github.com/openai-php/client/tree/v0.10.3"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/gehrisandro", "type": "github"}, {"url": "https://github.com/nunomaduro", "type": "github"}], "time": "2024-11-12T20:51:16+00:00"}, {"name": "openai-php/laravel", "version": "v0.10.2", "source": {"type": "git", "url": "https://github.com/openai-php/laravel.git", "reference": "24815ef1bda71cc0715f7aefe4506e77329e560f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/openai-php/laravel/zipball/24815ef1bda71cc0715f7aefe4506e77329e560f", "reference": "24815ef1bda71cc0715f7aefe4506e77329e560f", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.9.2", "laravel/framework": "^9.46.0|^10.34.2|^11.23.5", "openai-php/client": "^0.10.2", "php": "^8.1.0"}, "require-dev": {"laravel/pint": "^1.17.3", "pestphp/pest": "^2.35.1|^3.0.0", "pestphp/pest-plugin-arch": "^2.7.0|^3.0.0", "phpstan/phpstan": "^1.12.4", "symfony/var-dumper": "^6.4.0|^7.1.4"}, "type": "library", "extra": {"laravel": {"providers": ["OpenAI\\Laravel\\ServiceProvider"]}}, "autoload": {"psr-4": {"OpenAI\\Laravel\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "OpenAI PHP for Laravel is a supercharged PHP API client that allows you to interact with the Open AI API", "keywords": ["GPT-3", "api", "client", "codex", "dall-e", "language", "laravel", "natural", "openai", "php", "processing", "sdk"], "support": {"issues": "https://github.com/openai-php/laravel/issues", "source": "https://github.com/openai-php/laravel/tree/v0.10.2"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/gehrisandro", "type": "github"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2024-10-17T20:34:07+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/df1e7fde177501eee2037dd159cf04f5f301a512", "reference": "df1e7fde177501eee2037dd159cf04f5f301a512", "shasum": ""}, "require": {"php": "^8"}, "require-dev": {"phpunit/phpunit": "^9", "vimeo/psalm": "^4|^5"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2024-05-08T12:36:18+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "paragonie/sodium_compat", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/paragonie/sodium_compat.git", "reference": "a673d5f310477027cead2e2f2b6db5d8368157cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/sodium_compat/zipball/a673d5f310477027cead2e2f2b6db5d8368157cb", "reference": "a673d5f310477027cead2e2f2b6db5d8368157cb", "shasum": ""}, "require": {"php": "^8.1", "php-64bit": "*"}, "require-dev": {"phpunit/phpunit": "^7|^8|^9", "vimeo/psalm": "^4|^5"}, "suggest": {"ext-sodium": "Better performance, password hashing (Argon2i), secure memory management (memzero), and better security."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"files": ["autoload.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Pure PHP implementation of libsodium; uses the PHP extension if it exists", "keywords": ["Authentication", "BLAKE2b", "ChaCha20", "ChaCha20-Poly1305", "Chapoly", "Curve25519", "Ed25519", "EdDSA", "Edwards-curve Digital Signature Algorithm", "Elliptic Curve <PERSON>-<PERSON><PERSON>", "Poly1305", "Pure-PHP cryptography", "RFC 7748", "RFC 8032", "Salpoly", "Salsa20", "X25519", "XChaCha20-Poly1305", "XSalsa20-Poly1305", "Xchacha20", "Xsalsa20", "aead", "cryptography", "ecdh", "elliptic curve", "elliptic curve cryptography", "encryption", "libsodium", "php", "public-key cryptography", "secret-key cryptography", "side-channel resistant"], "support": {"issues": "https://github.com/paragonie/sodium_compat/issues", "source": "https://github.com/paragonie/sodium_compat/tree/v2.1.0"}, "time": "2024-09-04T12:51:01+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "php-http/discovery", "version": "1.20.0", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/82fe4c73ef3363caed49ff8dd1539ba06044910d", "reference": "82fe4c73ef3363caed49ff8dd1539ba06044910d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0", "zendframework/zend-diactoros": "*"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "*", "psr/http-factory-implementation": "*", "psr/http-message-implementation": "*"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1 || ^7.3", "sebastian/comparator": "^3.0.5 || ^4.0.8", "symfony/phpunit-bridge": "^6.4.4 || ^7.0.1"}, "type": "composer-plugin", "extra": {"class": "Http\\Discovery\\Composer\\Plugin", "plugin-optional": true}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}, "exclude-from-classmap": ["src/Composer/Plugin.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds and installs PSR-7, PSR-17, PSR-18 and HTTPlug implementations", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr17", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.20.0"}, "time": "2024-10-02T11:20:13+00:00"}, {"name": "php-http/multipart-stream-builder", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/php-http/multipart-stream-builder.git", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/multipart-stream-builder/zipball/10086e6de6f53489cca5ecc45b6f468604d3460e", "reference": "10086e6de6f53489cca5ecc45b6f468604d3460e", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/discovery": "^1.15", "psr/http-factory-implementation": "^1.0"}, "require-dev": {"nyholm/psr7": "^1.0", "php-http/message": "^1.5", "php-http/message-factory": "^1.0.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"Http\\Message\\MultipartStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A builder class that help you create a multipart stream", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "multipart stream", "stream"], "support": {"issues": "https://github.com/php-http/multipart-stream-builder/issues", "source": "https://github.com/php-http/multipart-stream-builder/tree/1.4.2"}, "time": "2024-09-04T13:22:54+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.29.11", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "05b6c4378ddf3e81b460ea645c42b46432c0db25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/05b6c4378ddf3e81b460ea645c42b46432c0db25", "reference": "05b6c4378ddf3e81b460ea645c42b46432c0db25", "shasum": ""}, "require": {"composer/pcre": "^1||^2||^3", "ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.15", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^7.4 || ^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^1.0 || ^2.0 || ^3.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5 || ^9.0", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.29.11"}, "time": "2025-06-23T01:22:06+00:00"}, {"name": "phpoption/phpoption", "version": "1.9.3", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/e3fac8b24f56113f7cb96af14958c0dd16330f54", "reference": "e3fac8b24f56113f7cb96af14958c0dd16330f54", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20 || ^10.5.28"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2024-07-20T21:41:07+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.45", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "bd81b90d5963c6b9d87de50357585375223f4dd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/bd81b90d5963c6b9d87de50357585375223f4dd8", "reference": "bd81b90d5963c6b9d87de50357585375223f4dd8", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2|^3", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-dom": "Install the DOM extension to load XML formatted public keys.", "ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.45"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2025-06-22T22:54:43+00:00"}, {"name": "phpunit/php-code-coverage", "version": "9.2.32", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/85402a822d1ecf1db1096959413d35e1c37cf1a5", "reference": "85402a822d1ecf1db1096959413d35e1c37cf1a5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^4.19.1 || ^5.1.0", "php": ">=7.3", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-text-template": "^2.0.4", "sebastian/code-unit-reverse-lookup": "^2.0.3", "sebastian/complexity": "^2.0.3", "sebastian/environment": "^5.1.5", "sebastian/lines-of-code": "^1.0.4", "sebastian/version": "^3.0.2", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^9.6"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "9.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/9.2.32"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-08-22T04:23:01+00:00"}, {"name": "phpunit/php-file-iterator", "version": "3.0.6", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "reference": "cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/3.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2021-12-02T12:48:52+00:00"}, {"name": "phpunit/php-invoker", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "reference": "5a10147d0aaf65b58940a0b72f71c9ac0423cc67", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/3.1.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:58:55+00:00"}, {"name": "phpunit/php-text-template", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "reference": "5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T05:33:50+00:00"}, {"name": "phpunit/php-timer", "version": "5.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "reference": "5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/5.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:16:10+00:00"}, {"name": "phpunit/phpunit", "version": "9.6.23", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "43d2cb18d0675c38bd44982a5d1d88f6d53d8d95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/43d2cb18d0675c38bd44982a5d1d88f6d53d8d95", "reference": "43d2cb18d0675c38bd44982a5d1d88f6d53d8d95", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5.0 || ^2", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.1", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=7.3", "phpunit/php-code-coverage": "^9.2.32", "phpunit/php-file-iterator": "^3.0.6", "phpunit/php-invoker": "^3.1.1", "phpunit/php-text-template": "^2.0.4", "phpunit/php-timer": "^5.0.3", "sebastian/cli-parser": "^1.0.2", "sebastian/code-unit": "^1.0.8", "sebastian/comparator": "^4.0.8", "sebastian/diff": "^4.0.6", "sebastian/environment": "^5.1.5", "sebastian/exporter": "^4.0.6", "sebastian/global-state": "^5.0.7", "sebastian/object-enumerator": "^4.0.4", "sebastian/resource-operations": "^3.0.4", "sebastian/type": "^3.2.1", "sebastian/version": "^3.0.2"}, "suggest": {"ext-soap": "To be able to generate mocks based on WSDL files", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "9.6-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/9.6.23"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-05-02T06:40:34+00:00"}, {"name": "predis/predis", "version": "v1.1.10", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "reference": "a2fb02d738bedadcffdbb07efa3a5e7bd57f8d6e", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "Flexible and feature-complete Redis client for PHP and HHVM", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v1.1.10"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-01-05T17:46:08+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "psy/psysh", "version": "v0.12.9", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "1b801844becfe648985372cb4b12ad6840245ace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/1b801844becfe648985372cb4b12ad6840245ace", "reference": "1b801844becfe648985372cb4b12ad6840245ace", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.9"}, "time": "2025-06-23T02:35:06+00:00"}, {"name": "pusher/pusher-php-server", "version": "7.2.7", "source": {"type": "git", "url": "https://github.com/pusher/pusher-http-php.git", "reference": "148b0b5100d000ed57195acdf548a2b1b38ee3f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/pusher/pusher-http-php/zipball/148b0b5100d000ed57195acdf548a2b1b38ee3f7", "reference": "148b0b5100d000ed57195acdf548a2b1b38ee3f7", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "guzzlehttp/guzzle": "^7.2", "paragonie/sodium_compat": "^1.6|^2.0", "php": "^7.3|^8.0", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"overtrue/phplint": "^2.3", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"Pusher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Library for interacting with the Pusher REST API", "keywords": ["events", "messaging", "php-pusher-server", "publish", "push", "pusher", "real time", "real-time", "realtime", "rest", "trigger"], "support": {"issues": "https://github.com/pusher/pusher-http-php/issues", "source": "https://github.com/pusher/pusher-http-php/tree/7.2.7"}, "time": "2025-01-06T10:56:20+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/collection", "version": "2.1.1", "source": {"type": "git", "url": "https://github.com/ramsey/collection.git", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/collection/zipball/344572933ad0181accbf4ba763e85a0306a8c5e2", "reference": "344572933ad0181accbf4ba763e85a0306a8c5e2", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.45", "fakerphp/faker": "^1.24", "hamcrest/hamcrest-php": "^2.0", "jangregor/phpstan-prophecy": "^2.1", "mockery/mockery": "^1.6", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy-phpunit": "^2.3", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^10.5", "ramsey/coding-standard": "^2.3", "ramsey/conventional-commits": "^1.6", "roave/security-advisories": "dev-latest"}, "type": "library", "extra": {"captainhook": {"force-install": true}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\Collection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for representing and manipulating collections.", "keywords": ["array", "collection", "hash", "map", "queue", "set"], "support": {"issues": "https://github.com/ramsey/collection/issues", "source": "https://github.com/ramsey/collection/tree/2.1.1"}, "time": "2025-03-22T05:38:12+00:00"}, {"name": "ramsey/uuid", "version": "4.9.0", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/4e0e23cc785f0724a0e838279a9eb03f28b092a0", "reference": "4e0e23cc785f0724a0e838279a9eb03f28b092a0", "shasum": ""}, "require": {"brick/math": "^0.8.8 || ^0.9 || ^0.10 || ^0.11 || ^0.12 || ^0.13", "php": "^8.0", "ramsey/collection": "^1.2 || ^2.0"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/plugin-composer": "^5.3", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "ergebnis/composer-normalize": "^2.47", "mockery/mockery": "^1.6", "paragonie/random-lib": "^2", "php-mock/php-mock": "^2.6", "php-mock/php-mock-mockery": "^1.5", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpbench/phpbench": "^1.2.14", "phpstan/extension-installer": "^1.4", "phpstan/phpstan": "^2.1", "phpstan/phpstan-mockery": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^9.6", "slevomat/coding-standard": "^8.18", "squizlabs/php_codesniffer": "^3.13"}, "suggest": {"ext-bcmath": "Enables faster math with arbitrary-precision integers using BCMath.", "ext-gmp": "Enables faster math with arbitrary-precision integers using GMP.", "ext-uuid": "Enables the use of PeclUuidTimeGenerator and PeclUuidRandomGenerator.", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"captainhook": {"force-install": true}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Ramsey\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A PHP library for generating and working with universally unique identifiers (UUIDs).", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid/tree/4.9.0"}, "time": "2025-06-25T14:20:11+00:00"}, {"name": "rap2hpoutre/laravel-log-viewer", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/rap2hpoutre/laravel-log-viewer.git", "reference": "ab85d5d88de728b99a9391a0c70e9b2f5743a589"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rap2hpoutre/laravel-log-viewer/zipball/ab85d5d88de728b99a9391a0c70e9b2f5743a589", "reference": "ab85d5d88de728b99a9391a0c70e9b2f5743a589", "shasum": ""}, "require": {"illuminate/support": "^6.0|^7.0|^8.0|^9.0|^10.0|^11.0|^12.0", "php": "^7.2|^8.0"}, "require-dev": {"orchestra/testbench": "^4.0|^5.0|^6.0|^7.0|^8.0|^9.0|^10.0", "phpunit/phpunit": "^7||^8.4|^9.3.3|^10.1|^11.0"}, "type": "laravel-package", "extra": {"laravel": {"providers": ["Rap2hpoutre\\LaravelLogViewer\\LaravelLogViewerServiceProvider"]}}, "autoload": {"psr-0": {"Rap2hpoutre\\LaravelLogViewer\\": "src/"}, "classmap": ["src/controllers"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "rap2hpoutre", "email": "rap<PERSON><PERSON><PERSON>@gmail.com"}], "description": "A Laravel log reader", "keywords": ["laravel", "log", "log-reader", "log-viewer", "logging", "lumen"], "support": {"issues": "https://github.com/rap2hpoutre/laravel-log-viewer/issues", "source": "https://github.com/rap2hpoutre/laravel-log-viewer/tree/v2.5.0"}, "time": "2025-03-10T15:35:06+00:00"}, {"name": "riverline/multipart-parser", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Riverline/multipart-parser.git", "reference": "1410f23a8fd416a0cf5c8867ea9c95544016c831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Riverline/multipart-parser/zipball/1410f23a8fd416a0cf5c8867ea9c95544016c831", "reference": "1410f23a8fd416a0cf5c8867ea9c95544016c831", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"laminas/laminas-diactoros": "^1.8.7 || ^2.11.1", "phpunit/phpunit": "^5.7 || ^9.0", "psr/http-message": "^1.0", "symfony/psr-http-message-bridge": "^1.1 || ^2.0"}, "type": "library", "autoload": {"psr-4": {"Riverline\\MultiPartParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Riverline", "homepage": "http://www.riverline.fr"}], "description": "One class library to parse multipart content with encoding and charset support.", "keywords": ["http", "multipart", "parser"], "support": {"issues": "https://github.com/Riverline/multipart-parser/issues", "source": "https://github.com/Riverline/multipart-parser/tree/2.2.0"}, "time": "2025-04-29T08:38:14+00:00"}, {"name": "sebastian/cli-parser", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "reference": "2b56bea83a09de3ac06bb18b92f068e60cc6f50b", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:27:43+00:00"}, {"name": "sebastian/code-unit", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/code-unit.git", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/code-unit/zipball/1fc9f64c0927627ef78ba436c9b17d967e68e120", "reference": "1fc9f64c0927627ef78ba436c9b17d967e68e120", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/code-unit", "support": {"issues": "https://github.com/sebastian<PERSON>mann/code-unit/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit/tree/1.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:08:54+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "reference": "ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T05:30:19+00:00"}, {"name": "sebastian/comparator", "version": "4.0.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "fa0f136dd2334583309d32b62544682ee972b51a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/comparator/zipball/fa0f136dd2334583309d32b62544682ee972b51a", "reference": "fa0f136dd2334583309d32b62544682ee972b51a", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/4.0.8"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2022-09-14T12:41:17+00:00"}, {"name": "sebastian/complexity", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/25f207c40d62b8b7aa32f5ab026c53561964053a", "reference": "25f207c40d62b8b7aa32f5ab026c53561964053a", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:19:30+00:00"}, {"name": "sebastian/diff", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/ba01945089c3a293b01ba9badc29ad55b106b0bc", "reference": "ba01945089c3a293b01ba9badc29ad55b106b0bc", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:30:58+00:00"}, {"name": "sebastian/environment", "version": "5.1.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "reference": "830c43a844f1f8d5b7a1f6d6076b784454d8b7ed", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/5.1.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:03:51+00:00"}, {"name": "sebastian/exporter", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/78c00df8f170e02473b682df15bfcdacc3d32d72", "reference": "78c00df8f170e02473b682df15bfcdacc3d32d72", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/4.0.6"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:33:00+00:00"}, {"name": "sebastian/global-state", "version": "5.0.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "reference": "bca7df1f32ee6fe93b4d4a9abbf69e13a4ada2c9", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^9.3"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/5.0.7"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T06:35:11+00:00"}, {"name": "sebastian/lines-of-code", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/lines-of-code/zipball/e1e4a170560925c26d424b6a03aed157e7dcc5c5", "reference": "e1e4a170560925c26d424b6a03aed157e7dcc5c5", "shasum": ""}, "require": {"nikic/php-parser": "^4.18 || ^5.0", "php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/1.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-12-22T06:20:34+00:00"}, {"name": "sebastian/object-enumerator", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/5c9eeac41b290a3712d88851518825ad78f45c71", "reference": "5c9eeac41b290a3712d88851518825ad78f45c71", "shasum": ""}, "require": {"php": ">=7.3", "sebastian/object-reflector": "^2.0", "sebastian/recursion-context": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/4.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:12:34+00:00"}, {"name": "sebastian/object-reflector", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "reference": "b4f479ebdbf63ac605d183ece17d8d7fe49c15c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/2.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-10-26T13:14:26+00:00"}, {"name": "sebastian/recursion-context", "version": "4.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "reference": "e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/4.0.5"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:07:39+00:00"}, {"name": "sebastian/resource-operations", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "reference": "05d5692a7993ecccd56a03e40cd7e5b09b1d404e", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-14T16:00:52+00:00"}, {"name": "sebastian/type", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/type/zipball/75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "reference": "75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "source": "https://github.com/sebastian<PERSON>mann/type/tree/3.2.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2023-02-03T06:13:03+00:00"}, {"name": "sebastian/version", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "c6c1022351a901512170118436c764e473f6de8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/c6c1022351a901512170118436c764e473f6de8c", "reference": "c6c1022351a901512170118436c764e473f6de8c", "shasum": ""}, "require": {"php": ">=7.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/3.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-09-28T06:39:44+00:00"}, {"name": "stripe/stripe-php", "version": "v13.18.0", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "02abb043b103766f4ed920642ae56ffdc58c7467"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/02abb043b103766f4ed920642ae56ffdc58c7467", "reference": "02abb043b103766f4ed920642ae56ffdc58c7467", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.5.0", "phpstan/phpstan": "^1.2", "phpunit/phpunit": "^5.7 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/v13.18.0"}, "time": "2024-04-09T21:08:04+00:00"}, {"name": "symfony/console", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "reference": "7d29659bc3c9d8e9a34e2c3414ef9e9e003e6cf3", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/dependency-injection": "<5.4", "symfony/dotenv": "<5.4", "symfony/event-dispatcher": "<5.4", "symfony/lock": "<5.4", "symfony/process": "<5.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^5.4|^6.0|^7.0", "symfony/messenger": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-07T07:05:04+00:00"}, {"name": "symfony/css-selector", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/error-handler", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "ce765a2d28b3cce61de1fb916e207767a73171d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/ce765a2d28b3cce61de1fb916e207767a73171d1", "reference": "ce765a2d28b3cce61de1fb916e207767a73171d1", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^5.4|^6.0|^7.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-28T12:00:15+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/497f73ac996a598c92409b44ac43b6690c4f666d", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-22T09:11:45+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/finder", "version": "v6.4.17", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "reference": "1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7", "shasum": ""}, "require": {"php": ">=8.1"}, "require-dev": {"symfony/filesystem": "^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v6.4.17"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-29T13:51:37+00:00"}, {"name": "symfony/http-foundation", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae", "reference": "6b7c97fe1ddac8df3cc9ba6410c8abc683e148ae", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^2.13.1|^3|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4.12|^6.0.12|^6.1.4|^7.0", "symfony/mime": "^5.4|^6.0|^7.0", "symfony/rate-limiter": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-11T15:36:20+00:00"}, {"name": "symfony/http-kernel", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "15c105b839a7cfa1bc0989c091bfb6477f23b673"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/15c105b839a7cfa1bc0989c091bfb6477f23b673", "reference": "15c105b839a7cfa1bc0989c091bfb6477f23b673", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<5.4", "symfony/cache": "<5.4", "symfony/config": "<6.1", "symfony/console": "<5.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<5.4", "symfony/form": "<5.4", "symfony/http-client": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<5.4", "symfony/messenger": "<5.4", "symfony/translation": "<5.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<5.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.3", "twig/twig": "<2.13"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^5.4|^6.0|^7.0", "symfony/clock": "^6.2|^7.0", "symfony/config": "^6.1|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/css-selector": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^5.4|^6.0|^7.0", "symfony/property-access": "^5.4.5|^6.0.5|^7.0", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.4|^7.0.4", "symfony/stopwatch": "^5.4|^6.0|^7.0", "symfony/translation": "^5.4|^6.0|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^5.4|^6.0|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^5.4|^6.4|^7.0", "symfony/var-exporter": "^6.2|^7.0", "twig/twig": "^2.13|^3.0.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:23:40+00:00"}, {"name": "symfony/mailer", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/ada2809ccd4ec27aba9fc344e3efdaec624c6438", "reference": "ada2809ccd4ec27aba9fc344e3efdaec624c6438", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.1", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^5.4|^6.0|^7.0", "symfony/mime": "^6.2|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/messenger": "<6.2", "symfony/mime": "<6.2", "symfony/twig-bridge": "<6.2.1"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0", "symfony/http-client": "^5.4|^6.0|^7.0", "symfony/messenger": "^6.2|^7.0", "symfony/twig-bridge": "^6.2|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-26T23:47:35+00:00"}, {"name": "symfony/mime", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/fec8aa5231f3904754955fad33c2db50594d22d1", "reference": "fec8aa5231f3904754955fad33c2db50594d22d1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<5.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.4|^7.0", "symfony/property-access": "^5.4|^6.0|^7.0", "symfony/property-info": "^5.4|^6.0|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T13:27:38+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/a3cc8b044a6ea513310cbd48ef7333b384945638", "reference": "a3cc8b044a6ea513310cbd48ef7333b384945638", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "763d2a91fea5681509ca01acbc1c5e450d127811"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/763d2a91fea5681509ca01acbc1c5e450d127811", "reference": "763d2a91fea5681509ca01acbc1c5e450d127811", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-21T18:38:29+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/9614ac4d8061dc257ecc64cba1b140873dce8ad3", "reference": "9614ac4d8061dc257ecc64cba1b140873dce8ad3", "shasum": ""}, "require": {"php": ">=7.2", "symfony/polyfill-intl-normalizer": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-10T14:38:51+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/2fb86d65e2d424369ad2905e83b236a8805ba491", "reference": "2fb86d65e2d424369ad2905e83b236a8805ba491", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v6.4.20", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/e2a61c16af36c9a07e5c9906498b73e091949a20", "reference": "e2a61c16af36c9a07e5c9906498b73e091949a20", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v6.4.20"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-10T17:11:00+00:00"}, {"name": "symfony/psr-http-message-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/psr-http-message-bridge.git", "reference": "03f2f72319e7acaf2a9f6fcbe30ef17eec51594f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/psr-http-message-bridge/zipball/03f2f72319e7acaf2a9f6fcbe30ef17eec51594f", "reference": "03f2f72319e7acaf2a9f6fcbe30ef17eec51594f", "shasum": ""}, "require": {"php": ">=8.2", "psr/http-message": "^1.0|^2.0", "symfony/http-foundation": "^6.4|^7.0"}, "conflict": {"php-http/discovery": "<1.15", "symfony/http-kernel": "<6.4"}, "require-dev": {"nyholm/psr7": "^1.1", "php-http/discovery": "^1.15", "psr/log": "^1.1.4|^2|^3", "symfony/browser-kit": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\PsrHttpMessage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "PSR HTTP message bridge", "homepage": "https://symfony.com", "keywords": ["http", "http-message", "psr-17", "psr-7"], "support": {"source": "https://github.com/symfony/psr-http-message-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-26T08:57:56+00:00"}, {"name": "symfony/routing", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/1f5234e8457164a3a0038a4c0a4ba27876a9c670", "reference": "1f5234e8457164a3a0038a4c0a4ba27876a9c670", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"doctrine/annotations": "<1.12", "symfony/config": "<6.2", "symfony/dependency-injection": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.12|^2", "psr/log": "^1|^2|^3", "symfony/config": "^6.2|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/expression-language": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-27T16:08:38+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/string", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f3570b8c61ca887a9e2938e85cb6458515d2b125", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}, {"name": "symfony/translation", "version": "v6.4.22", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "7e3b3b7146c6fab36ddff304a8041174bf6e17ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/7e3b3b7146c6fab36ddff304a8041174bf6e17ad", "reference": "7e3b3b7146c6fab36ddff304a8041174bf6e17ad", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^2.5|^3.0"}, "conflict": {"symfony/config": "<5.4", "symfony/console": "<5.4", "symfony/dependency-injection": "<5.4", "symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<5.4", "symfony/service-contracts": "<2.5", "symfony/twig-bundle": "<5.4", "symfony/yaml": "<5.4"}, "provide": {"symfony/translation-implementation": "2.3|3.0"}, "require-dev": {"nikic/php-parser": "^4.18|^5.0", "psr/log": "^1|^2|^3", "symfony/config": "^5.4|^6.0|^7.0", "symfony/console": "^5.4|^6.0|^7.0", "symfony/dependency-injection": "^5.4|^6.0|^7.0", "symfony/finder": "^5.4|^6.0|^7.0", "symfony/http-client-contracts": "^2.5|^3.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/intl": "^5.4|^6.0|^7.0", "symfony/polyfill-intl-icu": "^1.21", "symfony/routing": "^5.4|^6.0|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v6.4.22"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-29T07:06:44+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00"}, {"name": "symfony/uid", "version": "v6.4.13", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "18eb207f0436a993fffbdd811b5b8fa35fa5e007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/18eb207f0436a993fffbdd811b5b8fa35fa5e007", "reference": "18eb207f0436a993fffbdd811b5b8fa35fa5e007", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v6.4.13"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:18:03+00:00"}, {"name": "symfony/var-dumper", "version": "v6.4.21", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "22560f80c0c5cd58cc0bcaf73455ffd81eb380d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/22560f80c0c5cd58cc0bcaf73455ffd81eb380d5", "reference": "22560f80c0c5cd58cc0bcaf73455ffd81eb380d5", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<5.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^5.4|^6.0|^7.0", "symfony/error-handler": "^6.3|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "symfony/uid": "^5.4|^6.0|^7.0", "twig/twig": "^2.13|^3.0.4"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v6.4.21"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-09T07:34:50+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}, {"name": "tightenco/ziggy", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/tighten/ziggy.git", "reference": "0b3b521d2c55fbdb04b6721532f7f5f49d32f52b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tighten/ziggy/zipball/0b3b521d2c55fbdb04b6721532f7f5f49d32f52b", "reference": "0b3b521d2c55fbdb04b6721532f7f5f49d32f52b", "shasum": ""}, "require": {"ext-json": "*", "laravel/framework": ">=9.0", "php": ">=8.1"}, "require-dev": {"laravel/folio": "^1.1", "orchestra/testbench": "^7.0 || ^8.0 || ^9.0 || ^10.0", "pestphp/pest": "^2.26|^3.0", "pestphp/pest-plugin-laravel": "^2.4|^3.0"}, "type": "library", "extra": {"laravel": {"providers": ["Tighten\\Ziggy\\ZiggyServiceProvider"]}}, "autoload": {"psr-4": {"Tighten\\Ziggy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Use your Laravel named routes in JavaScript.", "homepage": "https://github.com/tighten/ziggy", "keywords": ["<PERSON><PERSON><PERSON>", "javascript", "laravel", "routes"], "support": {"issues": "https://github.com/tighten/ziggy/issues", "source": "https://github.com/tighten/ziggy/tree/v2.5.3"}, "time": "2025-05-17T18:15:19+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "0d72ac1c00084279c1816675284073c5a337c20d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/0d72ac1c00084279c1816675284073c5a337c20d", "reference": "0d72ac1c00084279c1816675284073c5a337c20d", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^7.4 || ^8.0", "symfony/css-selector": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpunit/phpunit": "^8.5.21 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/v2.3.0"}, "time": "2024-12-21T16:25:41+00:00"}, {"name": "vlucas/phpdotenv", "version": "v5.6.2", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "reference": "24ac4c74f91ee2c193fa1aaa5c249cb0822809af", "shasum": ""}, "require": {"ext-pcre": "*", "graham-campbell/result-type": "^1.1.3", "php": "^7.2.5 || ^8.0", "phpoption/phpoption": "^1.9.3", "symfony/polyfill-ctype": "^1.24", "symfony/polyfill-mbstring": "^1.24", "symfony/polyfill-php80": "^1.24"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-filter": "*", "phpunit/phpunit": "^8.5.34 || ^9.6.13 || ^10.4.2"}, "suggest": {"ext-filter": "Required to use the boolean validator."}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "5.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/vlucas"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v5.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2025-04-30T23:37:27+00:00"}, {"name": "voku/portable-ascii", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/voku/portable-ascii.git", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/voku/portable-ascii/zipball/b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "reference": "b1d923f88091c6bf09699efcd7c8a1b1bfd7351d", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "type": "library", "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://www.moelleken.org/"}], "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "homepage": "https://github.com/voku/portable-ascii", "keywords": ["ascii", "clean", "php"], "support": {"issues": "https://github.com/voku/portable-ascii/issues", "source": "https://github.com/voku/portable-ascii/tree/2.0.3"}, "funding": [{"url": "https://www.paypal.me/moelleken", "type": "custom"}, {"url": "https://github.com/voku", "type": "github"}, {"url": "https://opencollective.com/portable-ascii", "type": "open_collective"}, {"url": "https://www.patreon.com/voku", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/voku/portable-ascii", "type": "tidelift"}], "time": "2024-11-21T01:49:47+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "barryvdh/laravel-debugbar", "version": "v3.15.4", "source": {"type": "git", "url": "https://github.com/barryvdh/laravel-debugbar.git", "reference": "c0667ea91f7185f1e074402c5788195e96bf8106"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/laravel-debugbar/zipball/c0667ea91f7185f1e074402c5788195e96bf8106", "reference": "c0667ea91f7185f1e074402c5788195e96bf8106", "shasum": ""}, "require": {"illuminate/routing": "^9|^10|^11|^12", "illuminate/session": "^9|^10|^11|^12", "illuminate/support": "^9|^10|^11|^12", "php": "^8.1", "php-debugbar/php-debugbar": "~2.1.1", "symfony/finder": "^6|^7"}, "require-dev": {"mockery/mockery": "^1.3.3", "orchestra/testbench-dusk": "^7|^8|^9|^10", "phpunit/phpunit": "^9.5.10|^10|^11", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "extra": {"laravel": {"aliases": {"Debugbar": "Barryvdh\\Debugbar\\Facades\\Debugbar"}, "providers": ["Barryvdh\\Debugbar\\ServiceProvider"]}, "branch-alias": {"dev-master": "3.15-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Barryvdh\\Debugbar\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "PHP Debugbar integration for Laravel", "keywords": ["debug", "debugbar", "dev", "laravel", "profiler", "webprofiler"], "support": {"issues": "https://github.com/barryvdh/laravel-debugbar/issues", "source": "https://github.com/barryvdh/laravel-debugbar/tree/v3.15.4"}, "funding": [{"url": "https://fruitcake.nl", "type": "custom"}, {"url": "https://github.com/barryvdh", "type": "github"}], "time": "2025-04-16T06:32:06+00:00"}, {"name": "fakerphp/faker", "version": "v1.24.1", "source": {"type": "git", "url": "https://github.com/FakerPHP/Faker.git", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FakerPHP/Faker/zipball/e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "reference": "e0ee18eb1e6dc3cda3ce9fd97e5a0689a88a64b5", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "psr/container": "^1.0 || ^2.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "conflict": {"fzaninotto/faker": "*"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "doctrine/persistence": "^1.3 || ^2.0", "ext-intl": "*", "phpunit/phpunit": "^9.5.26", "symfony/phpunit-bridge": "^5.4.16"}, "suggest": {"doctrine/orm": "Required to use Faker\\ORM\\Doctrine", "ext-curl": "Required by Faker\\Provider\\Image to download images.", "ext-dom": "Required by Faker\\Provider\\HtmlLorem for generating random HTML.", "ext-iconv": "Required by Faker\\Provider\\ru_RU\\Text::realText() for generating real Russian text.", "ext-mbstring": "Required for multibyte Unicode string functionality."}, "type": "library", "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/FakerPHP/Faker/issues", "source": "https://github.com/FakerPHP/Faker/tree/v1.24.1"}, "time": "2024-11-21T13:46:39+00:00"}, {"name": "filp/whoops", "version": "2.18.3", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "59a123a3d459c5a23055802237cb317f609867e5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/59a123a3d459c5a23055802237cb317f609867e5", "reference": "59a123a3d459c5a23055802237cb317f609867e5", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "require-dev": {"mockery/mockery": "^1.0", "phpunit/phpunit": "^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.18.3"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2025-06-16T00:02:10+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.1.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "reference": "f8b1c0173b22fa6ec77a81fe63e5b01eba7e6487", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0 || ^3.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.1.1"}, "time": "2025-04-30T06:54:44+00:00"}, {"name": "laravel/pint", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/laravel/pint.git", "reference": "941d1927c5ca420c22710e98420287169c7bcaf7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/pint/zipball/941d1927c5ca420c22710e98420287169c7bcaf7", "reference": "941d1927c5ca420c22710e98420287169c7bcaf7", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "ext-tokenizer": "*", "ext-xml": "*", "php": "^8.2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.75.0", "illuminate/view": "^11.44.7", "larastan/larastan": "^3.4.0", "laravel-zero/framework": "^11.36.1", "mockery/mockery": "^1.6.12", "nunomaduro/termwind": "^2.3.1", "pestphp/pest": "^2.36.0"}, "bin": ["builds/pint"], "type": "project", "autoload": {"psr-4": {"App\\": "app/", "Database\\Seeders\\": "database/seeders/", "Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An opinionated code formatter for PHP.", "homepage": "https://laravel.com", "keywords": ["format", "formatter", "lint", "linter", "php"], "support": {"issues": "https://github.com/laravel/pint/issues", "source": "https://github.com/laravel/pint"}, "time": "2025-05-08T08:38:12+00:00"}, {"name": "laravel/sail", "version": "v1.43.1", "source": {"type": "git", "url": "https://github.com/laravel/sail.git", "reference": "3e7d899232a8c5e3ea4fc6dee7525ad583887e72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/sail/zipball/3e7d899232a8c5e3ea4fc6dee7525ad583887e72", "reference": "3e7d899232a8c5e3ea4fc6dee7525ad583887e72", "shasum": ""}, "require": {"illuminate/console": "^9.52.16|^10.0|^11.0|^12.0", "illuminate/contracts": "^9.52.16|^10.0|^11.0|^12.0", "illuminate/support": "^9.52.16|^10.0|^11.0|^12.0", "php": "^8.0", "symfony/console": "^6.0|^7.0", "symfony/yaml": "^6.0|^7.0"}, "require-dev": {"orchestra/testbench": "^7.0|^8.0|^9.0|^10.0", "phpstan/phpstan": "^1.10"}, "bin": ["bin/sail"], "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Sail\\SailServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Sail\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Docker files for running a basic Laravel application.", "keywords": ["docker", "laravel"], "support": {"issues": "https://github.com/laravel/sail/issues", "source": "https://github.com/laravel/sail"}, "time": "2025-05-19T13:19:21+00:00"}, {"name": "laravel/telescope", "version": "v4.17.6", "source": {"type": "git", "url": "https://github.com/laravel/telescope.git", "reference": "2d453dc629b27e8cf39fb1217aba062f8c54e690"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/telescope/zipball/2d453dc629b27e8cf39fb1217aba062f8c54e690", "reference": "2d453dc629b27e8cf39fb1217aba062f8c54e690", "shasum": ""}, "require": {"ext-json": "*", "laravel/framework": "^8.37|^9.0|^10.0", "php": "^8.0", "symfony/var-dumper": "^5.0|^6.0"}, "require-dev": {"ext-gd": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "laravel/octane": "^1.4", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["Laravel\\Telescope\\TelescopeServiceProvider"]}, "branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Laravel\\Telescope\\": "src/", "Laravel\\Telescope\\Database\\Factories\\": "database/factories/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "An elegant debug assistant for the Laravel framework.", "keywords": ["debugging", "laravel", "monitoring"], "support": {"issues": "https://github.com/laravel/telescope/issues", "source": "https://github.com/laravel/telescope/tree/v4.17.6"}, "time": "2024-02-08T15:04:38+00:00"}, {"name": "mockery/mockery", "version": "1.6.12", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/1f4efdd7d3beafe9807b08156dfcb176d18f1699", "reference": "1f4efdd7d3beafe9807b08156dfcb176d18f1699", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=7.3"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.6.17", "symplify/easy-coding-standard": "^12.1.14"}, "type": "library", "autoload": {"files": ["library/helpers.php", "library/Mockery.php"], "psr-4": {"Mockery\\": "library/Mockery"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/padraic", "role": "Author"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://davedevelopment.co.uk", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/ghostwriter", "role": "Lead Developer"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"docs": "https://docs.mockery.io/", "issues": "https://github.com/mockery/mockery/issues", "rss": "https://github.com/mockery/mockery/releases.atom", "security": "https://github.com/mockery/mockery/security/advisories", "source": "https://github.com/mockery/mockery"}, "time": "2024-05-16T03:13:13+00:00"}, {"name": "nunomaduro/collision", "version": "v7.12.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "995245421d3d7593a6960822063bdba4f5d7cf1a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/995245421d3d7593a6960822063bdba4f5d7cf1a", "reference": "995245421d3d7593a6960822063bdba4f5d7cf1a", "shasum": ""}, "require": {"filp/whoops": "^2.17.0", "nunomaduro/termwind": "^1.17.0", "php": "^8.1.0", "symfony/console": "^6.4.17"}, "conflict": {"laravel/framework": ">=11.0.0"}, "require-dev": {"brianium/paratest": "^7.4.8", "laravel/framework": "^10.48.29", "laravel/pint": "^1.21.2", "laravel/sail": "^1.41.0", "laravel/sanctum": "^3.3.3", "laravel/tinker": "^2.10.1", "nunomaduro/larastan": "^2.10.0", "orchestra/testbench-core": "^8.35.0", "pestphp/pest": "^2.36.0", "phpunit/phpunit": "^10.5.36", "sebastian/environment": "^6.1.0", "spatie/laravel-ignition": "^2.9.1"}, "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"files": ["./src/Adapters/Phpunit/Autoload.php"], "psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2025-03-14T22:35:49+00:00"}, {"name": "php-debugbar/php-debugbar", "version": "v2.1.6", "source": {"type": "git", "url": "https://github.com/php-debugbar/php-debugbar.git", "reference": "16fa68da5617220594aa5e33fa9de415f94784a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-debugbar/php-debugbar/zipball/16fa68da5617220594aa5e33fa9de415f94784a0", "reference": "16fa68da5617220594aa5e33fa9de415f94784a0", "shasum": ""}, "require": {"php": "^8", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^4|^5|^6|^7"}, "require-dev": {"dbrekelmans/bdi": "^1", "phpunit/phpunit": "^8|^9", "symfony/panther": "^1|^2.1", "twig/twig": "^1.38|^2.7|^3.0"}, "suggest": {"kriswallsmith/assetic": "The best way to manage assets", "monolog/monolog": "Log using Monolog", "predis/predis": "Redis storage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"DebugBar\\": "src/DebugBar/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "maxime.bouroume<PERSON>@gmail.com", "homepage": "http://maximebf.com"}, {"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "Debug bar in the browser for php application", "homepage": "https://github.com/php-debugbar/php-debugbar", "keywords": ["debug", "debug bar", "debugbar", "dev"], "support": {"issues": "https://github.com/php-debugbar/php-debugbar/issues", "source": "https://github.com/php-debugbar/php-debugbar/tree/v2.1.6"}, "time": "2025-02-21T17:47:03+00:00"}, {"name": "spatie/backtrace", "version": "1.7.4", "source": {"type": "git", "url": "https://github.com/spatie/backtrace.git", "reference": "cd37a49fce7137359ac30ecc44ef3e16404cccbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/backtrace/zipball/cd37a49fce7137359ac30ecc44ef3e16404cccbe", "reference": "cd37a49fce7137359ac30ecc44ef3e16404cccbe", "shasum": ""}, "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"ext-json": "*", "laravel/serializable-closure": "^1.3 || ^2.0", "phpunit/phpunit": "^9.3 || ^11.4.3", "spatie/phpunit-snapshot-assertions": "^4.2 || ^5.1.6", "symfony/var-dumper": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Backtrace\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "A better backtrace", "homepage": "https://github.com/spatie/backtrace", "keywords": ["Backtrace", "spatie"], "support": {"source": "https://github.com/spatie/backtrace/tree/1.7.4"}, "funding": [{"url": "https://github.com/sponsors/spatie", "type": "github"}, {"url": "https://spatie.be/open-source/support-us", "type": "other"}], "time": "2025-05-08T15:41:09+00:00"}, {"name": "spatie/error-solutions", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/spatie/error-solutions.git", "reference": "e495d7178ca524f2dd0fe6a1d99a1e608e1c9936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/error-solutions/zipball/e495d7178ca524f2dd0fe6a1d99a1e608e1c9936", "reference": "e495d7178ca524f2dd0fe6a1d99a1e608e1c9936", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"illuminate/broadcasting": "^10.0|^11.0|^12.0", "illuminate/cache": "^10.0|^11.0|^12.0", "illuminate/support": "^10.0|^11.0|^12.0", "livewire/livewire": "^2.11|^3.5.20", "openai-php/client": "^0.10.1", "orchestra/testbench": "8.22.3|^9.0|^10.0", "pestphp/pest": "^2.20|^3.0", "phpstan/phpstan": "^2.1", "psr/simple-cache": "^3.0", "psr/simple-cache-implementation": "^3.0", "spatie/ray": "^1.28", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "simple-cache-implementation": "To cache solutions from OpenAI"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Ignition\\": "legacy/ignition", "Spatie\\ErrorSolutions\\": "src", "Spatie\\LaravelIgnition\\": "legacy/laravel-ignition"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "This is my package error-solutions", "homepage": "https://github.com/spatie/error-solutions", "keywords": ["error-solutions", "spatie"], "support": {"issues": "https://github.com/spatie/error-solutions/issues", "source": "https://github.com/spatie/error-solutions/tree/1.1.3"}, "funding": [{"url": "https://github.com/Spatie", "type": "github"}], "time": "2025-02-14T12:29:50+00:00"}, {"name": "spatie/flare-client-php", "version": "1.10.1", "source": {"type": "git", "url": "https://github.com/spatie/flare-client-php.git", "reference": "bf1716eb98bd689451b071548ae9e70738dce62f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/flare-client-php/zipball/bf1716eb98bd689451b071548ae9e70738dce62f", "reference": "bf1716eb98bd689451b071548ae9e70738dce62f", "shasum": ""}, "require": {"illuminate/pipeline": "^8.0|^9.0|^10.0|^11.0|^12.0", "php": "^8.0", "spatie/backtrace": "^1.6.1", "symfony/http-foundation": "^5.2|^6.0|^7.0", "symfony/mime": "^5.2|^6.0|^7.0", "symfony/process": "^5.2|^6.0|^7.0", "symfony/var-dumper": "^5.2|^6.0|^7.0"}, "require-dev": {"dms/phpunit-arraysubset-asserts": "^0.5.0", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "spatie/pest-plugin-snapshots": "^1.0|^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.3.x-dev"}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\FlareClient\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/spatie/flare-client-php", "keywords": ["exception", "flare", "reporting", "spatie"], "support": {"issues": "https://github.com/spatie/flare-client-php/issues", "source": "https://github.com/spatie/flare-client-php/tree/1.10.1"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2025-02-14T13:42:06+00:00"}, {"name": "spatie/ignition", "version": "1.15.1", "source": {"type": "git", "url": "https://github.com/spatie/ignition.git", "reference": "31f314153020aee5af3537e507fef892ffbf8c85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/ignition/zipball/31f314153020aee5af3537e507fef892ffbf8c85", "reference": "31f314153020aee5af3537e507fef892ffbf8c85", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^8.0", "spatie/error-solutions": "^1.0", "spatie/flare-client-php": "^1.7", "symfony/console": "^5.4|^6.0|^7.0", "symfony/var-dumper": "^5.4|^6.0|^7.0"}, "require-dev": {"illuminate/cache": "^9.52|^10.0|^11.0|^12.0", "mockery/mockery": "^1.4", "pestphp/pest": "^1.20|^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "psr/simple-cache-implementation": "*", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/process": "^5.4|^6.0|^7.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "simple-cache-implementation": "To cache solutions from OpenAI"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.5.x-dev"}}, "autoload": {"psr-4": {"Spatie\\Ignition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for PHP applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/ignition/issues", "source": "https://github.com/spatie/ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2025-02-21T14:31:39+00:00"}, {"name": "spatie/laravel-ignition", "version": "2.9.1", "source": {"type": "git", "url": "https://github.com/spatie/laravel-ignition.git", "reference": "1baee07216d6748ebd3a65ba97381b051838707a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-ignition/zipball/1baee07216d6748ebd3a65ba97381b051838707a", "reference": "1baee07216d6748ebd3a65ba97381b051838707a", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "illuminate/support": "^10.0|^11.0|^12.0", "php": "^8.1", "spatie/ignition": "^1.15", "symfony/console": "^6.2.3|^7.0", "symfony/var-dumper": "^6.2.3|^7.0"}, "require-dev": {"livewire/livewire": "^2.11|^3.3.5", "mockery/mockery": "^1.5.1", "openai-php/client": "^0.8.1|^0.10", "orchestra/testbench": "8.22.3|^9.0|^10.0", "pestphp/pest": "^2.34|^3.7", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan-deprecation-rules": "^1.1.1|^2.0", "phpstan/phpstan-phpunit": "^1.3.16|^2.0", "vlucas/phpdotenv": "^5.5"}, "suggest": {"openai-php/client": "Require get solutions from OpenAI", "psr/simple-cache-implementation": "Needed to cache solutions from OpenAI"}, "type": "library", "extra": {"laravel": {"aliases": {"Flare": "Spatie\\LaravelIgnition\\Facades\\Flare"}, "providers": ["Spatie\\LaravelIgnition\\IgnitionServiceProvider"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"Spatie\\LaravelIgnition\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A beautiful error page for Laravel applications.", "homepage": "https://flareapp.io/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/spatie/laravel-ignition/issues", "source": "https://github.com/spatie/laravel-ignition"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2025-02-20T13:13:55+00:00"}, {"name": "symfony/yaml", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "cea40a48279d58dc3efee8112634cb90141156c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/cea40a48279d58dc3efee8112634cb90141156c2", "reference": "cea40a48279d58dc3efee8112634cb90141156c2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T10:10:33+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"ghazniali95/shopifyconnector": 20}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": "^8.2", "ext-json": "*", "ext-zip": "*"}, "platform-dev": [], "plugin-api-version": "2.3.0"}