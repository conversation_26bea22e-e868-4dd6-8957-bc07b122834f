import { useState, useEffect } from 'react';
import { usePage } from '@inertiajs/react';
import axios from 'axios';

/**
 * Custom hook for managing notification badge functionality
 * Fetches unread notification count for the authenticated user's organization
 */
export const useNotifications = () => {
    const [unreadCount, setUnreadCount] = useState(0);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    
    // Get user data from Inertia shared props
    const { props } = usePage();
    const authUser = props.authUser;
    const organization = props.organization;

    // Function to fetch notification count from API
    const fetchNotificationCount = async () => {
        try {
            setLoading(true);
            setError(null);
            
            const response = await axios.get('/api/2024-12/notifications/unread-count');
            setUnreadCount(response.data.count || 0);
        } catch (err) {
            console.error('Error fetching notification count:', err);
            setError(err.message);
            setUnreadCount(0);
        } finally {
            setLoading(false);
        }
    };

    // Alternative method using Inertia shared data (if available)
    const getNotificationCountFromProps = () => {
        // If notification count is passed through Inertia props, use it
        if (props.unreadNotificationCount !== undefined) {
            setUnreadCount(props.unreadNotificationCount);
            setLoading(false);
            return true;
        }
        return false;
    };

    // Fetch notification count on component mount and when user/organization changes
    useEffect(() => {
        if (!authUser || !organization) {
            setUnreadCount(0);
            setLoading(false);
            return;
        }

        // Try to get count from props first, fallback to API
        if (!getNotificationCountFromProps()) {
            fetchNotificationCount();
        }
    }, [authUser?.id, organization?.id]);

    // Function to manually refresh notification count
    const refreshNotificationCount = () => {
        if (authUser && organization) {
            fetchNotificationCount();
        }
    };

    // Function to mark notifications as read (decrements count)
    const markAsRead = (count = 1) => {
        setUnreadCount(prev => Math.max(0, prev - count));
    };

    return {
        unreadCount,
        loading,
        error,
        hasUnreadNotifications: unreadCount > 0,
        refreshNotificationCount,
        markAsRead,
        isAuthenticated: !!authUser,
        organizationId: organization?.id
    };
};

export default useNotifications;
