import React from "react";
import { createRoot } from "react-dom/client";
import { createInertiaApp } from "@inertiajs/react";
import "../js/globals.css";
import "../css/notification-badge.css";
import { route } from "ziggy-js";
import { Ziggy } from "./ziggy";
import { Link } from "@inertiajs/react";
import ReduxProvider from "./store/Provider";

window.route = (name, params, absolute) => route(name, params, absolute, Ziggy);
window.Link = Link;

// Configure Vite HMR to use network IP instead of localhost
if (import.meta.hot) {
    import.meta.hot.on("vite:beforeUpdate", () => {
        console.log("vite:beforeUpdate");
    });
}

createInertiaApp({
    title: (title) => (title ? `Apimio - ${title}` : "Apimio"),
    resolve: (name) => {

console.log(name);
        /**
         * 1. Your main app pages:
         */
        const localPages = import.meta.glob(["./Pages/**/*.jsx", "./**/*.jsx", "./components/**/*.jsx"]);

        /**
         * 2. Conditionally include MappingModule from local or vendor:
         */
        const localMapping = import.meta.glob("../../packages/apimio/mapping-fields-package/resources/js/pages/**/*.jsx");
        const vendorMapping = import.meta.glob("../../vendor/apimio/mapping-connector-package/resources/js/pages/**/*.jsx");

        // Decide which glob to use based on environment
        const mappingPages =
            import.meta.env.VITE_APP_ENV === "local" // or process.env.APP_ENV
                ? import.meta.glob("../../packages/apimio/mapping-fields-package/resources/js/pages/**/*.jsx")
                : import.meta.glob("../../vendor/apimio/mapping-connector-package/resources/js/pages/**/*.jsx");

        // Merge everything into a single object
        const pages = {
            ...localPages,
            ...mappingPages,
        };

   

        /**
         * 3. Now resolve the requested Inertia page/component
         */
        // If you have a naming pattern "pages/foo" or "components/foo", let's strip out the prefixes:
        const v2Pages = name.replace("pages/", "");

        const v2mappingpages =
            import.meta.env.VITE_APP_ENV === "local"
                ? "../../packages/apimio/mapping-fields-package/resources/js/"
                : "../../vendor/apimio/mapping-connector-package/resources/js/";

        // Attempt to find a matching path in the pages
        let page = pages[`./Pages/${name}.jsx`] || pages[`./${v2Pages}.jsx`] || pages[`${v2mappingpages}${name}.jsx`];

        // Special case for auth/ManualLinking
        if (name === "auth/ManualLinking") {
            page = pages["./Pages/auth/ManualLinking.jsx"];
        }

        /**
         * If "page" is still null, maybe the request is actually a direct reference
         * to the local or vendor component (e.g. "MappingModule").
         * Let's try constructing the path keys for your local/vendor globs:
         */
        if (!page) {
            const localKey = `../../packages/apimio/mapping-fields-package/resources/js/pages/${name}.jsx`;
            const vendorKey = `../../vendor/apimio/mapping-connector-package/resources/js/pages/${name}.jsx`;
            page = pages[localKey] || pages[vendorKey];
        }
        // Try both paths

        if (!page) {
            throw new Error(`Page not found: ${name}`);
        }

        // Finally, return the dynamic import
        return page().then((module) => module.default);
    },

    setup({ el, App, props }) {
        const root = createRoot(el);
        root.render(
            <ReduxProvider>
                <App {...props} />
            </ReduxProvider>
        );
    },
});
