import React, { useState, useEffect, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { VariableSizeList as List } from "react-window";
import MappingForm from "./MappingFormRedux";
import SubmitModal from "./producttabsfields/SubmitModal";
import { ToastContainer, toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import "../../../css/mapping-styles.css";
import CreateAttributeModal from "./producttabsfields/CreateAttributeModal";
import AddVLookupModal from "./producttabsfields/AddVLookupModal";
import { Button } from "antd";
import axios from "axios";
import {
  addNode,
  calculateCounts,
  createMappingNodes,
  calculateWarning,
  setWarning,
  setNodes,
  addNewAttribute,
  setLoading,
  updateNode,
} from "../reduxStore/nodeSlice";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import store from "../reduxStore/store";

const MappingModule = ({ data }) => {
  const [submitModal, setSubmitModal] = useState(0);
  const [selectedCatalog, setSelectedCatalog] = useState([]);
  const [tempName, setTempName] = useState("");
  const [tempId, setTempId] = useState("");
  const [ignoreUnmapped, setIgnoreUnmapped] = useState(false);
  const [dataAll, SetDataAll] = useState(data);
  const [openVLookupModal, setOpenVLookupModal] = useState(0);
  const handleIgnoreUnmappedChange = (e) => {
    setIgnoreUnmapped(e.target.checked);
  };
  const loading = useSelector((state) => state.nodes.loading);
  const dispatch = useDispatch();
  // Fetching the nodes from the Redux store
  const { nodes, warning, rowCount, totalRowCount } = useSelector(
    (state) => state.nodes
  );
  const [allFamilies, setAllFamilies] = useState(
    data.apimio_attributes_required?.all_families
      ? data.apimio_attributes_required.all_families
      : []
  );
  const [allAttributes, setAllAttributes] = useState(
    data.apimio_attributes_required?.all_attributes
      ? data.apimio_attributes_required.all_attributes
      : []
  );
  const [rowsLimit, setRowsLimit] = useState(false);
  const handleResetClick = useCallback(() => {
    const firstFourNodes = nodes.slice(0, 4); // Keep only the first 4 nodes
    // Dispatch the action to update the nodes in the Redux store
    dispatch(setNodes({ nodes: firstFourNodes }));

    //dispatch(updateNode(firstFourNodes));
  }, [nodes, dispatch]);
  // Fetching whether the "Add New Row" button should be disabled
  const listRef = useRef();
  const getItemSize = (index) => {
    const item = nodes[index];

    // Check if any value in an array is empty
    const isAnyValueEmpty = (value) => {
      if (Array.isArray(value)) {
        return value.length === 0 || value.some((v) => v === ""); // Check if the array is empty or contains any empty element
      }
      return value === ""; // If it's not an array, check the single value
    };

    // Calculate if there are empty values in each field
    const anyEmptyFromValue = isAnyValueEmpty(item.from);
    const anyEmptyToValue = isAnyValueEmpty(item.to);
    const anyEmptyWithValue = isAnyValueEmpty(item.with);
    const anyEmptyReplaceValue = isAnyValueEmpty(item.replace);

    // Determine the height based on the formula type and the presence of empty values
    let height;

    if (
      item.with_formula === "split" ||
      item.with_formula === "calculate" ||
      item.with_formula === "vlookup"
    ) {
      if (anyEmptyToValue || anyEmptyFromValue || anyEmptyWithValue) {
        height = 120; // Height when there's a missing value in to, from, or with
      } else {
        height = 90; // Approximate height for no warning
      }
    } else if (
      item.with_formula === "replace" ||
      item.with_formula === "expand"
    ) {
      if (
        anyEmptyToValue ||
        anyEmptyFromValue ||
        anyEmptyReplaceValue ||
        anyEmptyWithValue
      ) {
        height = 120; // Height when there's a missing value in to, from, replace, or with
      } else {
        height = 90; // Approximate height for no warning
      }
    } else if (item.with_formula === "short_code") {
      if (anyEmptyToValue || anyEmptyFromValue) {
        height = 170; // Height for 5 rows of text area
      } else {
        height = 140; // Approximate height for short_code without missing values
      }
    } else if (item.with_formula === "merge") {
      if (anyEmptyToValue || anyEmptyFromValue || item.from.length < 2) {
        height = 120; // Height for 5 rows of text area
      } else {
        height = 90; // Approximate height for short_code without missing values
      }
    } else {
      if (anyEmptyToValue || anyEmptyFromValue) {
        height = 120; // Height when there's a missing value in to or from for other formulas
      } else {
        height = 90; // Default height for no warning
      }
    }

    return height;
  };
  const handleCloseVlookupModal = () => {
    setOpenVLookupModal(0);
    fetchVLookupData();
  };

  const [attrModalIndex, setAttrModalIndex] = useState(0);
  const onattrCloseModal = (updatedData, formData, index) => {
    const newAttribute = formData.name;
    const lowerCaseAttribute = newAttribute
      .toLowerCase()
      .replace(/[{()}[\]<>]/g, "")
      .replace(/ /g, "_");
    const fullAttribute =
      formData.attribute_family_name + "," + lowerCaseAttribute;
    const rowIndex = index;
    setAllAttributes(updatedData.apimio_attributes_required.all_attributes);
    if (updatedData && updatedData.apimio_attributes_required) {
      setAllFamilies(updatedData.apimio_attributes_required.all_families);
    }
    setAttrModalIndex(0);

    dataAll.output_array = updatedData.output_array;
    // Add a delay to ensure that the state is updated before selecting the new attribute
    setTimeout(() => {
      dispatch(addNewAttribute({ rowIndex, fullAttribute }));
    }, 1200);
  };

  const [attributeIndex, setAttributeIndex] = useState(null);
  const handleAttributeChange = (index) => {
    setAttributeIndex(index);
    setAttrModalIndex(1);
  };
  const handleOpenVlookup = () => {
    setOpenVLookupModal(1);
  };
  const [vLookupData, setVLookupData] = useState([]);
  const fetchVLookupData = useCallback(async () => {
    try {
      const response = await axios.post("/vlookup/fetch");
      const data = response.data.data;
      setVLookupData(data);
    } catch (error) {
      toast.error("Error fetching VLookup data:", error);
    }
  }, []);
  useEffect(() => {
    if (dataAll) {
      dispatch(createMappingNodes(dataAll));
    }
  }, [dataAll, dispatch]);
  useEffect(() => {
    dispatch(calculateCounts());
    if (nodes.length >= 300) {
      setRowsLimit(true);
    } else {
      setRowsLimit(false);
    }
    console.log(nodes, "nodesupdated");
  }, [nodes, dispatch, rowCount, totalRowCount]);

  useEffect(() => {
    dispatch(setLoading(true));
    setTimeout(() => {
      dispatch(setLoading(false));
    }, 1000);
  }, [dispatch]);

  const handleHeightChange = (index) => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(index);
    }
  };

  // Function to add a new node
  const handleAddNewNode = () => {
    dispatch(addNode()); // Assuming `addNode` will add a node to the nodes array in Redux
    const endDiv = document.getElementById("end");
    setTimeout(() => {
      if (listRef.current) {
        const lastIndex = nodes.length;
        listRef.current.scrollToItem(lastIndex, "end"); // Scroll to the last item
      }
    }, 200);
    setTimeout(() => {
      if (endDiv) {
        endDiv.scrollIntoView({ behavior: "smooth", block: "end" });
      }
    }, 210);
  };
  const [listHeight, setListHeight] = useState(window.innerHeight - 135);
  const isNextDisabled = () => {
    if (rowCount == 0) return false; // All rows are mapped
    if (warning) return true; // Disable if there's a warning
    if (rowCount > 0 && !warning && ignoreUnmapped) return false; // Allow proceed

    return true; // Default: disable the button
  };
  useEffect(() => {
    // Update height on window resize
    const handleResize = () => setListHeight(window.innerHeight);
    window.addEventListener("resize", handleResize);

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  // Calculate row count and warning
  useEffect(() => {
    let count = 0;

    nodes.forEach((node) => {
      if (node.to == "" || node.from == "") {
        count += 1;
      }
    });
    dispatch(setWarning(calculateWarning(nodes, data.importAction)));
    isNextDisabled();
  }, [nodes, data]);

  const [saveTemplateClicked, setSaveTemplateClicked] = useState(false);
  const handleSaveTemplateClick = useCallback(() => {
    setSaveTemplateClicked(true);
    setSubmitModal(1);
  }, [dispatch]);
  const handleSaveTemplate = (templateData) => {
      SetDataAll((prevData) => ({
          ...prevData,
          template_attributes: templateData,
      }));
    setTempName(templateData.name);
    setTempId(templateData.id);
    const newSelectedCatalogs = {};
    if (Array.isArray(templateData.channel_id)) {
      // It's an array
      templateData.channel_id.forEach((channelId) => {
        // iterate through channel IDs
        const channelOption = Object.entries(data.data_required.catalogs).find(
          ([key]) => key == channelId
        ); // find the channel option based on ID
        if (channelOption) {
          // if a channel option is found
          const [channelId, channelLabel] = channelOption; // destructure the channel option
          newSelectedCatalogs[channelId] = {
            // save the channel ID and label
            value: channelId,
            label: channelLabel,
          };
        }
      });
    } else {
      // It's a string
      const channelId = templateData.channel_id;
      const channelLabel = data.data_required.catalogs[channelId];
      if (channelLabel) {
        newSelectedCatalogs[channelId] = {
          value: channelId,
          label: channelLabel,
        };
      }
    }
    setSelectedCatalog(Object.values(newSelectedCatalogs));
  };
  const handleNextClick = useCallback(() => {
    setSaveTemplateClicked(false);

    setSubmitModal(1);
  }, []);
  const hideModal = () => {
    setSubmitModal(0);
  };
  return (
    <div className="relative">
      <ToastContainer
        position="top-right"
        autoClose={2000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
      {loading && (
        <div className="loader">
          <div className="spinner"></div>
        </div>
      )}
      <div className="product-header pb-3 left-[15.438rem] w-[95%]]">
        <div className="flex formStyle justify-between items-center flex-wrap">
          <div className="tab-list mr-5 pr-5 items-center">
            <Button
              className="btn btn-outline-secondary mr-4 reset_fields"
              onClick={handleResetClick}
            >
              Reset
            </Button>
              <>
              {data.data_required.apimio_plans_access.template_save !== undefined &&
              data.data_required.apimio_plans_access.template_save == false ? (
                  // Disabled save template button for trial users
                  <Button
                      data-bs-toggle="tooltip"
                      data-bs-placement="top"
                      className="btn btn-primary float-end onclick-disabled-with-loader opacity-50"
                      data-bs-original-title="This feature is available only in the Advanced PIM package or higher."
                  >
                      Save Template
                  </Button>
              ) :
                      // Active save template button for paid users
                      <Button
                          danger
                          className="btn btn-outline-danger"
                          onClick={handleSaveTemplateClick}
                      >
                          Save Template
                      </Button>
              }
              </>

          </div>

          <div className="heading flex items-center">
            <div className="page-title text-center">
              <h2 className="font-24 block">Mapping Products</h2>
              <p className="block">Mapping conversion of all your products</p>
            </div>
          </div>

          <div className="flex items-center">
            <div className="warning-block">
              {warning && (
                <p id="handle_warning" className="text-danger mb-0">
                  <i className="fa fa-exclamation-triangle mr-2"></i>
                  {warning}
                </p>
              )}

              {rowCount != 0 && (
                <p className="mb-0 clr-grey row-count">
                  You have {rowCount} / {totalRowCount} unmapped columns
                </p>
              )}

              {rowCount === 0 && (
                <p className="mb-0 text-green-700 all-mapped font-bold">
                  All rows are Mapped now!
                </p>
              )}
              {rowCount !== 0 && (
                <div className="mb-0 d-flex align-items-center clr-grey proceed-data-switch">
                  <div className="form-check form-switch d-inline">
                    <input
                      name="ignore_unmapped"
                      id="ignore_unmapped"
                      type="checkbox"
                      className="custom-control-input form-check-input mt-1"
                      onChange={handleIgnoreUnmappedChange}
                    />
                    <label
                      className="custom-control-label custom-label ml-2 mt-1"
                      htmlFor="ignore_unmapped"
                    >
                      {!ignoreUnmapped ? (
                        <span className="ignore_unchecked">
                          Don't Skip Unmatched columns
                        </span>
                      ) : (
                        <span className="ignore_checked">
                          Skip Unmatched columns
                        </span>
                      )}
                    </label>
                  </div>
                </div>
              )}
            </div>

            <div className="ml-4 items-center">
              {
                data.data_required.template_method_type !== "shopify" ?
                  <Button
                    id="pro_imp_btn"
                    type="default"
                    style={{
                      backgroundColor: '#740898',
                      color: 'white',
                      borderColor: '#740898',
                      ':hover': {
                        backgroundColor: 'white !important',
                        color: '#740898 !important',
                        borderColor: '#740898 !important'
                      }
                    }}
                    size={"large"}
                    title="If this button is disabled, please make sure all your unmapped fields are mapped or mark the checkbox."
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    onClick={handleNextClick}
                    disabled={isNextDisabled()}
              >
                Next
                  </Button>
                  :
                  ""
              }

            </div>
          </div>
        </div>
      </div>

      {/* Virtualized List for rendering nodes */}
      <List
        ref={listRef}
        height={listHeight} // Height of the virtualized list container
        itemCount={nodes.length} // Number of items in the list
        itemSize={getItemSize} // Height of each row item
        width={"100%"} // Width of the list
        style={{ overflowX: "hidden" }}
        className="list-group"
      >
        {({ index, style }) => (
          <div
            style={{
              ...style,
            }}
            className="list-item mt-12"
            key={index}
          >
            {/* Pass the index to MappingForm to render each node */}
            <MappingForm
              key={`mapping-form-${index}`}
              index={index}
              data={dataAll}
              handleHeightChange={handleHeightChange}
              handleAttributeChange={handleAttributeChange}
              handleOpenVlookup={handleOpenVlookup}
            />
          </div>
        )}
      </List>
      <Button
        type={rowsLimit ? "" : "primary"}
        onClick={handleAddNewNode}
        disabled={rowsLimit}
        className="bg-[#740898] text-white border border-[#740898] rounded-[4px]"
      >
        Add New Row
      </Button>
      {rowsLimit && (
        <p className="text-red-500">
          Rows Limit Reached. Delete some rows to add more rows.
        </p>
      )}
      <div id="end"></div>
      {submitModal == 0 ? (
        ""
      ) : (
        <SubmitModal
          hideModal={hideModal}
          data={dataAll}
          submitData={nodes}
          tempName={tempName}
          setTempName={setTempName}
          tempId={tempId}
          setTempId={setTempId}
          selectedCatalog={selectedCatalog}
          setSelectedCatalog={setSelectedCatalog}
          onSaveTemplate={handleSaveTemplate}
          isSaveTemplate={saveTemplateClicked}
        />
      )}
      {openVLookupModal ? (
        <AddVLookupModal handleCloseVlookupModal={handleCloseVlookupModal} />
      ) : (
        ""
      )}
      {attrModalIndex ? (
        <CreateAttributeModal
          index={attributeIndex}
          allFamilies={allFamilies}
          allAttributes={allAttributes}
          onattrCloseModal={onattrCloseModal}
          attributeCloseModal={() => setAttrModalIndex(null)}
        />
      ) : (
        ""
      )}
      <div className="short_code_layover hidden fixed z-[99] bg-[#] w-screen h-screen top-0 left-0"></div>
    </div>
  );
};

export default MappingModule;

// Setting up the root element for React rendering
const container = document.getElementById("importmapping");
const root = createRoot(container);

root.render(
  <>
    <Provider store={store}>
      <MappingModule data={data} />
    </Provider>
  </>
);
