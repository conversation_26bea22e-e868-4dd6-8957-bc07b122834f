import React, { useEffect, useState } from "react";
import ReactD<PERSON> from "react-dom/client";
import axios from "axios";
import Filter from "./Filters";
import noProducts from "../../../../public/media/emptypage/empty_page.png";
import { getProgressColor } from "./utils/getProgressBarColor";
import { SearchOutlined, DeleteOutlined, CloseOutlined } from "@ant-design/icons";
import { Table, Button, Popconfirm, message, Progress, Spin, Tooltip } from "antd";

export default function ListingTable({
    filteredData,
    filteredPagination,
    showFilter = true,
    showDeleteColumn = true,
    showPagination = true,
    showCheckbox = true,
}) {
    // Define a single constant for page size
    const DEFAULT_PAGE_SIZE = 30;

    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        total: 0,
    });
    // State to track applied filters
    const [filtersApplied, setFiltersApplied] = useState(false);
    const [finalizedFilters, setFinalizedFilters] = useState(null);
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [deleteProductRouteTemplate, setDeleteProductRouteTemplate] = useState("");
    const [searchValue, setSearchValue] = useState("");
    const organizationId =
        document.getElementById("listingTable")?.getAttribute("data-orgId") ||
        document.getElementById("listingTable-dashboard")?.getAttribute("data-orgId");
    const orgIDFromLocalStorage = localStorage.getItem("orgId");

    const storedFilters = localStorage.getItem("filters");
    // const [assignFilterTotalProductCount, setAssignFilterTotalProductCount] = useState();

    // Define initializeData outside of useEffect so it can be referenced anywhere in the component
    const initializeData = async () => {
        const storedFilters = localStorage.getItem("filters");
        const urlParams = new URLSearchParams(window.location.search);
        const scoreFilter = urlParams.get("score");

        if (scoreFilter) {
            await fetchData(1, pagination.pageSize, null, "", scoreFilter);
        } else if (storedFilters) {
            if (document.getElementById("filter_total_product_array")) {
                document.getElementById("filter_total_product_array").value = storedFilters;
            }
            const parsedFilters = JSON.parse(storedFilters);
            await fetchData(1, pagination.pageSize, parsedFilters, searchValue);
            setFinalizedFilters(parsedFilters);
            setFiltersApplied(true);
        } else if (filteredData && filteredData.length > 0) {
            setData(filteredData);
            setPagination(filteredPagination);
            if (document.getElementById("filter_total_product_count")) {
                document.getElementById("filter_total_product_count").value = filteredPagination.total;
            }
        } else {
            let urlpage = 1;
            if (urlParams.has("page")) {
                urlpage = parseInt(urlParams.get("page"), 10) || 1;
            }
            await fetchData(urlpage, pagination.pageSize);
        }
    };

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const scoreFilter = urlParams.get("score");
        // If you need to perform an action after `searchValue` changes
        if (searchValue === "") {
            initializeData(); // Fetch with empty search
        }
    }, [searchValue]);

    useEffect(() => {
        // Ensure the element is present before accessing it
        if (!window.location.pathname.includes("/dashboard")) {
            const deleteRoute = document.getElementById("listingTable")?.getAttribute("data-delete-product-route");
            if (deleteRoute) {
                setDeleteProductRouteTemplate(deleteRoute);
            } else {
                console.error("Element with ID 'listingTable' not found or data attribute missing");
            }
        }

        if (orgIDFromLocalStorage && organizationId !== orgIDFromLocalStorage) {
            localStorage.removeItem("filters");
        }
    }, []);

    useEffect(() => {
        initializeData();
    }, [filteredData, filteredPagination]);

    const fetchData = (page = pagination.current, pageSize = DEFAULT_PAGE_SIZE, filters = null, search = "", scoreFilter = null) => {
        setLoading(true);
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get("page") !== String(page) && !window.location.pathname.includes("/dashboard")) {
            urlParams.set("page", page);
            window.history.pushState({}, "", `${window.location.pathname}?${urlParams.toString()}`);
        }

        let url = `/api/${import.meta.env.VITE_API_VERSION}/products/index?paginate=${pageSize}&page=${page}`;
        if (scoreFilter) {
            url += `&score=${scoreFilter}`;
        }
        axios
            .post(
                url,
                {
                    tableSearch: searchValue ?? null,
                    filters: filters,
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            )
            .then((response) => {
                console.log(response);
                const fetchedData = response.data.data;
                if (scoreFilter) {
                    // Filter data based on score
                    const filteredData = fetchedData.filter((item) => {
                        if (scoreFilter === "good") {
                            return item.version_score >= 90;
                        } else if (scoreFilter === "fair") {
                            return item.version_score >= 50 && item.version_score < 90;
                        } else if (scoreFilter === "bad") {
                            return item.version_score < 50;
                        }
                        return true;
                    });
                    setData(filteredData.map((item) => fetchedData.find((product) => product.id === item.id)));
                    setPagination({
                        current: 1,
                        pageSize: 5,
                        total: 250,
                    });
                } else {
                    if (window.location.pathname.includes("/dashboard") && fetchedData?.length > 6) {
                        setData(fetchedData.slice(0, 6)); // Limit the data to the first 6 items
                    } else {
                        setData(fetchedData); // Otherwise, set the full fetched data
                    }
                }
                setPagination({
                    current: response.data.pagination.current_page,
                    pageSize: response.data.pagination.per_page,
                    total: response.data.pagination.total,
                });
                // if (!window.location.pathname.includes("/dashboard")) {
                //     document.getElementById("filter_total_product_count").value = response.data.pagination.total;
                // }
                if ($("#filter_total_product_count").length) {
                    $("#filter_total_product_count").val(response.data.pagination.total);
                }
            })
            .catch((error) => {
                message.error("Server Busy, Try again Later");
            })
            .finally(() => {
                setLoading(false);
                setTimeout(() => {
                    $(".bulk_product_check").prop("checked", false).removeClass("checked");
                    $("#multiselect_products").prop("checked", false);
                    $(".bulk-footer").addClass("d-none");
                }, 0);
            });
    };

    const handleTableChange = (pagination) => {
        const { current, pageSize } = pagination;
        setPagination({ current, pageSize });
        const urlParams = new URLSearchParams(window.location.search);
        const scoreFilter = urlParams.get("score");
        fetchData(current, pageSize, finalizedFilters, searchValue, scoreFilter);
    };

    const clearFilters = () => {
        setFinalizedFilters(null); // Clear the filters
        setFiltersApplied(false); // Indicate no filters are applied
        localStorage.removeItem("filters");
        document.getElementById("filter_total_product_array").value = "";
        fetchData(1, DEFAULT_PAGE_SIZE); // Fetch the original data without filters
        message.success("Filter Cleared!!");
    };

    const handleDelete = async (productId) => {
        if (!deleteProductRouteTemplate) {
            console.error("Delete product route template not found.");
            return;
        }

        try {
            const deleteUrl = deleteProductRouteTemplate.replace(":id", productId);
            const response = await axios.delete(deleteUrl);

            if (response.status === 200) {
                const newData = data.filter((item) => item.id !== productId); // Remove deleted product from state
                setData(newData);
                message.success("Product deleted successfully!");

                // Re-fetch data to keep everything up-to-date
                fetchData(1, DEFAULT_PAGE_SIZE); // Use the default page size when re-fetching
            }
        } catch (error) {
            message.error("Failed to delete product. Please try again.");
            console.error("Error deleting product:", error);
        }
    };

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const scoreFilter = urlParams.get("score");
        // If you need to perform an action after `searchValue` changes
        if (searchValue === "") {
            fetchData(1, pagination.pageSize, finalizedFilters, null, scoreFilter); // Fetch with empty search
        }
    }, [searchValue]);

    const handleInputChange = (e) => {
        setSearchValue(e.target.value);
    };

    const handleSearchClick = () => {
        // You can pass any filters you need here as well
        fetchData(1, pagination.pageSize, finalizedFilters); // Trigger the API call with the current searchValue
    };

    const handleClearSearch = () => {
        // Clear the search value
        setSearchValue("");
    };

    const columns = [
        ...(showCheckbox
            ? [
                  {
                      title: (
                          <input
                              id="multiselect_products"
                              type="checkbox"
                              className="bulk-checkbox"
                              checked={selectedRowKeys.length === data?.length}
                              onChange={(e) => {
                                  if (e.target.checked) {
                                      const allProductIds = data.map((item) => item.id);
                                      setSelectedRowKeys(allProductIds); // Select all product IDs
                                  } else {
                                      setSelectedRowKeys([]); // Deselect all
                                  }
                              }}
                          />
                      ),
                      dataIndex: "checkbox",
                      key: "checkbox",
                      onCell: (record) => ({
                          onClick: (e) => e.stopPropagation(), // Prevent row click when clicking on this cell
                      }),
                      render: (text, record) => (
                          <input
                              type="checkbox"
                              id={record.id}
                              name="product_id"
                              data-id={record.id}
                              className={`bulk_product_check product Bulk_product_${record.id}`}
                              checked={selectedRowKeys.includes(record.id)}
                              onClick={(e) => e.stopPropagation()}
                              onChange={(e) => {
                                  const checked = e.target.checked;
                                  if (checked) {
                                      setSelectedRowKeys([...selectedRowKeys, record.id]);
                                  } else {
                                      setSelectedRowKeys(selectedRowKeys.filter((id) => id !== record.id));
                                  }
                              }}
                          />
                      ),
                  },
              ]
            : []),

        {
            title: "Image",
            dataIndex: "file.link",
            key: "file",
            render: (_, record) => {
                const defaultImage = "/img/apimio_default.jpg";
                const imageSrc = record.file?.link || defaultImage;

                // A small inline handler to replace the broken image with the default
                const handleImageError = (e) => {
                    e.target.src = defaultImage;
                };

                return (
                    <img
                        src={imageSrc}
                        alt={record.product_name}
                        onError={handleImageError}
                        style={{ width: "40px", height: "40px", objectFit: "cover" }}
                    />
                );
            },
        },
        {
            title: "Product Name",
            dataIndex: "product_name",
            key: "product_name",
            // Here we can specify a fixed width or use onCell to control style.
            // 'width' is recognized by antd but doesn't directly apply ellipsis.
            // We'll handle ellipsis via styling in onCell.
            onCell: () => {
                return {
                    style: {
                        // You can tweak these values to your needs
                        minWidth: "120px",
                        maxWidth: "250px",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                    },
                };
            },
            render: (text) => {
                // Truncate text to the first 30 chars
                const truncatedText = text && text.length > 30 ? text.substring(0, 30) + "..." : text;

                return (
                    <Tooltip title={text}>
                        {/*
            The parent span should be constrained to
            the same width settings (through the onCell style)
            so that it can properly show the ellipsis.
          */}
                        <span style={{ display: "inline-block", width: "100%" }}>{truncatedText}</span>
                    </Tooltip>
                );
            },
        },
        {
            title: "Status",
            dataIndex: "status",
            key: "status",
            render: (text, record) => <span className={`badge px-4 py-2 rounded-pill ${record.status_badge}`}>{record.status}</span>,
        },
        {
            title: "No of Variants",
            dataIndex: "variants_count",
            key: "variants_count",
        },
        {
            title: "Product Quality Score",
            dataIndex: "version_score",
            key: "version_score",
            render: (text, record) => (
                <Progress
                    style={{
                        width: "200px",
                    }}
                    percent={record.version_score}
                    size="small"
                    strokeColor={getProgressColor(record.version_score)}
                />
            ),
        },
        ...(showDeleteColumn
            ? [
                  {
                      title: "Action",
                      key: "action",
                      render: (text, record) => (
                          <Popconfirm
                              title="Are you sure to delete this product?"
                              onClick={(e) => e.stopPropagation()} // Prevent row click on popconfirm trigger
                              onConfirm={(e) => {
                                  e?.stopPropagation(); // Prevent row click on "Yes" button
                                  handleDelete(record.id);
                              }}
                              okText="Yes"
                              cancelText="No"
                              onCancel={(e) => e?.stopPropagation()} // Prevent row click on "No" button
                          >
                              <DeleteOutlined
                                  style={{
                                      color: "red",
                                      fontSize: 20,
                                  }}
                              />
                          </Popconfirm>
                      ),
                  },
              ]
            : []),
    ];
    console.log("products in old view", data);
    return loading ? (
        // Display loading spinner or any loading indicator
        <div
            style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "70vh", // Full viewport height for vertical centering
            }}
        >
            <Spin size="large" />
        </div>
    ) : data?.length === 0 && searchValue === "" && (!localStorage.getItem("filters") || localStorage.getItem("filters").length === 0) ? (
        // Render image if no products are available
        <div className="text-center mt-5 p-5">
            <img src={noProducts} alt="No products available" style={{ maxWidth: "100%", height: "auto" }} />
            <p>You have not added any products yet.</p>
        </div>
    ) : (
        <div className="mb-5">
            {showFilter && (
                <div className="d-flex justify-content-between">
                    <div className="d-flex align-items-center w-full">
                        <div className="input-group mb-3 position-relative">
                            <input
                                type="text"
                                className="form-control "
                                placeholder="Search"
                                onChange={handleInputChange}
                                value={searchValue}
                                aria-describedby="basic-addon2"
                                onKeyDown={(e) => {
                                    if (e.key === "Enter") {
                                        handleSearchClick();
                                    }
                                }}
                            />
                            {searchValue && (
                                <CloseOutlined
                                    style={{ position: "absolute", right: "45px", top: "10px", cursor: "pointer" }}
                                    onClick={handleClearSearch}
                                />
                            )}
                            <span className="input-group-text bg-dark text-white" style={{ cursor: "pointer" }} onClick={handleSearchClick}>
                                <SearchOutlined />
                            </span>
                        </div>
                    </div>
                    <div className="d-flex align-items-center w-full">
                        {(() => {
                            const urlParams = new URLSearchParams(window.location.search);
                            const scoreFilter = urlParams.get("score");
                            if (scoreFilter) {
                                return (
                                    <div className="mb-3 me-4 border p-2 rounded">
                                        <span className="border-end border-2 pe-3">
                                            Displaying products with {scoreFilter} quality score
                                        </span>
                                        <a
                                            onClick={() => {
                                                // Clear score filter from URL and fetch all products
                                                urlParams.delete("score");
                                                window.history.pushState({}, "", `${window.location.pathname}?${urlParams.toString()}`);
                                                fetchData(1, DEFAULT_PAGE_SIZE);
                                            }}
                                            type="default"
                                            className="ms-3"
                                            style={{ cursor: "pointer", textDecoration: "none" }}
                                        >
                                            Show All Products
                                        </a>
                                    </div>
                                );
                            }
                        })()}
                    </div>

                    <div className="d-flex">
                        {filtersApplied && (
                            <Button onClick={clearFilters} type="default" className="me-3">
                                Clear Filters
                            </Button>
                        )}
                        <Filter
                            onFilteredData={(filteredData, filteredPagination, filters) => {
                                setData(filteredData);
                                setPagination(filteredPagination);
                                setFinalizedFilters(filters);
                                setFiltersApplied(true);
                            }}
                        />
                    </div>
                </div>
            )}
            <div className="mt-1">
                <Table
                    size="small"
                    columns={columns}
                    dataSource={data}
                    loading={loading} // Table component can show its own loading state
                    rowKey={(record) => record.id}
                    pagination={
                        showPagination
                            ? {
                                  current: pagination.current,
                                  pageSize: pagination.pageSize,
                                  total: pagination.total,
                                  showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} products`,
                              }
                            : false
                    }
                    onChange={handleTableChange}
                    onRow={(record) => ({
                        onClick: () => {
                            if (!record.deleted) {
                                const url = `/products/${record.id}/edit`;
                                window.location.href = url;
                            }
                        },
                        style: { cursor: record.deleted ? "default" : "pointer" },
                    })}
                />
            </div>
        </div>
    );
}

const rootElement = document.getElementById("listingTable");
const dashboardTable = document.getElementById("listingTable-dashboard");

if (rootElement) {
    ReactDOM.createRoot(rootElement).render(<ListingTable />);
}

if (dashboardTable) {
    const showFilter = dashboardTable.getAttribute("data-show-search") === "true";
    const showPagination = dashboardTable.getAttribute("data-show-pagination") === "true";
    const showDeleteColumn = dashboardTable.getAttribute("data-show-delete") === "true";
    const showCheckbox = dashboardTable.getAttribute("data-show-checkbox") === "true";
    ReactDOM.createRoot(dashboardTable).render(
        <ListingTable
            showFilter={showFilter} // Make sure the prop matches the component definition
            showPagination={showPagination}
            showDeleteColumn={showDeleteColumn}
            showCheckbox={showCheckbox}
        />
    );
}
