import React from "react";
import { Row, Col, Button } from "antd";
import UpArrow from "../../../../public/v2/icons/uparrow-icon.svg";
import Doughnut<PERSON>hart from "./DoughnutChart";

const ProductQualityScores = ({ title, description, showHeader = true, className = "", scores }) => {
    if (!scores) return null;
    
    const cards = [
        {
            index: 1,
            value: scores.good,
            quality: "Good",
            description: "Products with 90% or Greater Quality Score.",
            buttonLabel: "Update",
            color: "#15D476",
            scoreParam: "good"
        },
        {
            index: 2,
            value: scores.fair,
            quality: "Fair",
            description: "Products with less than 90% and Greater than Quality Score.",
            buttonLabel: "Improve",
            color: "#FF9C3E",
            scoreParam: "fair"
        },
        {
            index: 3,
            value: scores.bad,
            quality: "Bad",
            description: "Products with less than 50% Quality Score.",
            buttonLabel: "Improve",
            color: "#FE1F23",
            scoreParam: "bad"
        },
    ];

    // Calculate total for percentage
    const total = scores.approve + scores.warning + scores.error;
    const percentage = Math.round((scores.approve / total) * 100);

    const handleNavigate = (scoreParam) => {
        window.location.href = `/products?score=${scoreParam}`;
    };

    return (
        <div className={`p-5 ${className}`}>
            {showHeader && (
                <>
                    <p className="text-[#252525] font-[600] text-[18px]">{title || "Product Quality Scores"}</p>
                    <p className="text-[#626262] font-normal text-[14px]">
                        {description || "Track Product Quality Metrics for Your Products."}
                    </p>
                </>
            )}
            <div className="py-5">
                <Row className="flex gap-5">
                    <Col flex="1" className="flex items-center justify-center">
                        <DoughnutChart qualityType="Product" scores={scores} />
                    </Col>
                    {cards.map((card) => (
                        <Col flex="1" key={card.index}>
                            <div className="h-[168px] p-5 text-center bg-white flex flex-col rounded-[12px] items-center justify-center border border-[#EBEBEB]">
                                <p className="text-[#252525] text-[40px] font-[700]">{card.value}</p>
                                <p className="text-[16px] font-[600]" style={{ color: card.color }}>
                                    {card.quality}
                                </p>
                                <p className="text-[#626262] font-normal text-[10px]">{card.description}</p>
                                <Button
                                    className="text-[14px] font-normal rounded-[16px]"
                                    style={{ borderColor: card.color, color: card.color }}
                                    onClick={() => handleNavigate(card.scoreParam)}
                                >
                                    <img src={UpArrow} alt="up arrow" /> {card.buttonLabel}
                                </Button>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
        </div>
    );
};

export default ProductQualityScores;
