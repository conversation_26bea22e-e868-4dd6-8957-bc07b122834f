import React from 'react';
import { Head } from '@inertiajs/react';
import Sidebar<PERSON>ithLogo from './layout/SidebarWithLogo';
import Sidebar from './layout/Sidebar';
import NotificationBadge from '../components/NotificationBadge';
import { useNotifications } from '../hooks/useNotifications';

/**
 * Test page for notification badge functionality
 * This page demonstrates both sidebar components with notification badges
 */
const TestNotificationBadge = () => {
    const { 
        unreadCount, 
        loading, 
        error, 
        hasUnreadNotifications, 
        refreshNotificationCount,
        isAuthenticated,
        organizationId 
    } = useNotifications();

    return (
        <>
            <Head title="Test Notification Badge" />
            
            <div className="min-h-screen bg-gray-100">
                <div className="flex">
                    {/* Sidebar with Logo */}
                    <div className="w-1/2">
                        <h2 className="text-lg font-semibold p-4 bg-white border-b">
                            Sidebar With Logo
                        </h2>
                        <SidebarWithLogo activeKey="/notification" />
                    </div>
                    
                    {/* Sidebar without Logo */}
                    <div className="w-1/2">
                        <h2 className="text-lg font-semibold p-4 bg-white border-b">
                            Sidebar Without Logo
                        </h2>
                        <Sidebar activeKey="/notification" />
                    </div>
                </div>
                
                {/* Debug Information */}
                <div className="fixed bottom-4 right-4 bg-white p-4 rounded-lg shadow-lg border max-w-sm">
                    <h3 className="font-semibold mb-2">Notification Debug Info</h3>
                    <div className="text-sm space-y-1">
                        <div>
                            <strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
                        </div>
                        <div>
                            <strong>Organization ID:</strong> {organizationId || 'N/A'}
                        </div>
                        <div>
                            <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
                        </div>
                        <div>
                            <strong>Error:</strong> {error || 'None'}
                        </div>
                        <div>
                            <strong>Unread Count:</strong> {unreadCount}
                        </div>
                        <div>
                            <strong>Has Unread:</strong> {hasUnreadNotifications ? 'Yes' : 'No'}
                        </div>
                        <div className="mt-2">
                            <button 
                                onClick={refreshNotificationCount}
                                className="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
                                disabled={loading}
                            >
                                {loading ? 'Loading...' : 'Refresh Count'}
                            </button>
                        </div>
                        
                        {/* Standalone Badge Examples */}
                        <div className="mt-3 pt-3 border-t">
                            <div className="font-semibold mb-2">Badge Examples:</div>
                            <div className="flex items-center gap-6">
                                <div className="relative inline-block">
                                    <span className="text-xs block mb-1">Dot Badge on Icon:</span>
                                    <div className="relative inline-block">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-gray-600">
                                            <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                                        </svg>
                                        <NotificationBadge className="-top-1 -right-1" position="absolute" />
                                    </div>
                                </div>
                                <div className="relative inline-block">
                                    <span className="text-xs block mb-1">Count Badge on Icon:</span>
                                    <div className="relative inline-block">
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-gray-600">
                                            <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
                                        </svg>
                                        <NotificationBadge className="-top-1 -right-1" position="absolute" showCount={true} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default TestNotificationBadge;
