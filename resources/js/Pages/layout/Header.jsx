import React, { useEffect, useState, useRef } from "react";
import { Layout, But<PERSON> } from "antd";
import Logo from "../../../../public/v2/images/logo.png";
import { Link, usePage } from "@inertiajs/react";
import SwitchOrganization from "../../../../public/v2/icons/switch-organization.svg";
const { Header } = Layout;
import { router } from "@inertiajs/react";

const AppHeader = () => {
    // Get user data and route information from usePage hook
    const { props, component, url } = usePage();
    const { auth, organization } = props;
    const user = auth?.user;
    const isAuthenticated = !!user;
    const [headerTitle, setHeaderTitle] = useState("");
    const previousTitleRef = useRef("");
    console.log("props", props);
    // Generate a title from the component name
    const getTitleFromComponent = () => {
        if (!component) return "";

        // Extract the component name from the path
        const pathParts = component.split("/");
        const componentName = pathParts[pathParts.length - 1];

        // Handle special cases
        if (componentName === "DashboardContent") return "Dashboard";

        // Convert component name to title case
        // Remove "Content" suffix if it exists
        let title = componentName.replace(/Content$/, "");

        // Handle camelCase or PascalCase conversion to separate words
        title = title.replace(/([A-Z])/g, " $1").trim();

        return title;
    };

    // Check if current page is a product page
    const isProductPage = () => {
        if (!component || !url) return false;
        return url.includes("/product/") || component.includes("product/") || (component.split("/").pop() || "").includes("Product");
    };

    // Safe update for title that prevents unnecessary re-renders
    const updateTitleIfChanged = (newTitle) => {
        if (newTitle && newTitle !== previousTitleRef.current) {
            previousTitleRef.current = newTitle;
            setHeaderTitle(newTitle);
        }
    };

    // Initial title setup on component/url change
    useEffect(() => {
        const initialTitle = getTitleFromComponent();

        // For product pages, we'll check the document title
        if (isProductPage()) {
            // First, set the component-based title if we don't have one yet
            if (!headerTitle) {
                updateTitleIfChanged(initialTitle);
            }

            // Then set up an interval to check for document.title changes
            // This is more reliable than setTimeout for catching title changes
            const intervalId = setInterval(() => {
                if (document.title) {
                    const tabTitle = document.title.replace("Apimio - ", "");
                    if (tabTitle) {
                        updateTitleIfChanged(tabTitle);
                        clearInterval(intervalId); // Once we have a title, stop checking
                    }
                }
            }, 50);

            return () => clearInterval(intervalId);
        } else {
            // For non-product pages, just use the component title
            updateTitleIfChanged(initialTitle);
        }
    }, [component, url]);

    // Display different content based on authentication status
    const renderUserInfo = () => {
        if (!isAuthenticated) {
            return <div className="text-sm font-[700] text-black mr-4">Guest User</div>;
        }

        return (
            <>
                <div className="w-8 h-8 rounded-[12px] bg-[#740898] m-[6px] flex items-center justify-center text-white uppercase">
                    {user.fname && user.lname
                        ? user.fname[0] + user.lname[0]
                        : user.fname
                            ? user.fname[0]
                            : user.lname
                                ? user.lname[0]
                                : ""}
                </div>
                <div className="flex flex-col mr-4">
                    <div className="text-[14px] leading-[14px] font-[700] text-black capitalize">
                        {user.fname && user.lname ? user.fname + " " + user.lname : user.fname ? user.fname : "User"}
                    </div>
                    {organization && (
                        <div className="text-[14px] leading-[14px] text-black font-[400] text-left">
                            {organization.name || "No organization selected"}
                        </div>
                    )}
                </div>
            </>
        );
    };

    return (
        <Header className="bg-white px-[30px] border border-[#D9D9D9] flex items-center justify-between h-[80px]">
            <div className="flex items-center">
                <span onClick={() => router.visit("/dashboard")}>
                    <img src={Logo} alt="Logo" className="w-36 cursor-pointer" />
                </span>
                {isAuthenticated && headerTitle && (
                    <div className="ml-6 pl-6 border-l border-gray-300 flex items-center">
                        <h1 className="text-[20px] font-[700] text-[#252525] m-0 ">{headerTitle}</h1>
                    </div>
                )}
            </div>
            <div className="flex items-center">
                {renderUserInfo()}
                {/* {isAuthenticated && (
                    <Link href="/organization/select" className="ml-2">
                        <Button
                            type="default"
                            className="border-[#740898] text-[#740898] hover:text-[#8c26ad] hover:border-[#8c26ad] flex items-center"
                            icon={<img src={SwitchOrganization} alt="Switch" className="mr-1 h-4 w-4" />}
                        ></Button>
                    </Link>
                )} */}
            </div> 
        </Header>
    );
};

export default AppHeader;
