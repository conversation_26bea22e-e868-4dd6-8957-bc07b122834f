/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.jsx",
        "./resources/**/*.js",
        "./resources/js/v2/**/*.jsx",  // specifically include v2 directory
        "./packages/apimio/mapping-fields-package/resources/js/**/*.{js,jsx}",
        "./packages/apimio/mapping-fields-package/resources/js/pages/**/*.{js,jsx}",
        "./packages/apimio/mapping-fields-package/resources/js/pages/components/**/*.{js,jsx}",
        "./packages/**/resources/**/*.{js,jsx,blade.php}",
    ],
    theme: {
        extend: {
            screens: {
                'md2': '1000px',
                '2xl2': { 'min': '1920px' },
            },
        },
    },
    plugins: [],
    corePlugins: {
        preflight: false, // This prevents Tailwind from conflicting with Ant Design
    },
};
